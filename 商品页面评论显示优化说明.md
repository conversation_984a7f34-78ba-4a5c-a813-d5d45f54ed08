# 商品页面评论显示优化说明

## 修改内容

将商品页面的评论显示从详细评论内容改为简化的评论条数显示，点击后跳转到评论详情页面。

## 修改文件

### 1. app/wjhx/pages/goods/goods.wxml

**修改前：**
- 显示评论的详细内容（用户头像、昵称、时间、评论内容、图片等）
- 占用较多页面空间

**修改后：**
- 只显示"商品评价"标题和评论条数
- 简洁的一行显示，包含查看全部按钮和箭头图标
- 点击整个区域都可以跳转到评论详情页面

### 2. app/wjhx/pages/goods/goods.wxss

**新增样式：**
- `.comment-summary`: 评论摘要容器样式
- `.comment-info`: 评论信息区域样式
- `.comment-title`: 评论标题样式
- `.comment-count`: 评论条数样式
- `.comment-action`: 操作区域样式
- `.view-all`: 查看全部文字样式

## 功能特点

1. **简洁显示**：只显示评论条数，不占用过多页面空间
2. **清晰导航**：明确的"查看全部"提示和箭头图标
3. **交互反馈**：点击时有视觉反馈效果
4. **保持功能**：跳转到评论详情页面的功能保持不变

## 显示效果

```
┌─────────────────────────────────────┐
│ 商品评价 (15条)          查看全部 → │
└─────────────────────────────────────┘
```

## 跳转逻辑

- 跳转路径：`../comment/comment?valueId={{goods.id}}&typeId=0`
- 传递商品ID和类型ID到评论页面
- 评论页面会显示该商品的所有评论详情

## 优势

1. **页面简洁**：减少页面内容，提升浏览体验
2. **加载速度**：减少页面渲染内容，提升加载速度
3. **用户体验**：用户可以选择是否查看详细评论
4. **移动友好**：在小屏幕上显示更加合理

## 兼容性

- 保持原有的跳转逻辑不变
- 评论数据获取逻辑不变
- 只修改显示方式，不影响其他功能