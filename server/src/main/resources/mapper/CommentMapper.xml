<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.CommentMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.Comment">
        <!--
      WARNING - @mbg.generated
    -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="type_id" property="typeId" jdbcType="TINYINT"/>
        <result column="value_id" property="valueId" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="new_content" property="newContent" jdbcType="VARCHAR"/>
        <result column="rating" property="rating" jdbcType="TINYINT"/>
    </resultMap>
    <select id="selectIfRequirePictureList" resultMap="BaseResultMap" parameterType="com.logic.code.model.query.CommentQuery">
        SELECT
        id,type_id,value_id,content,create_time,status,user_id,new_content,rating
        FROM
        weshop_comment wc
        where
        type_id=#{commentQuery.typeId}
        and value_id=#{commentQuery.valueId}
        <if test="commentQuery.requirePicture">
            AND wc.id IN (
            SELECT
            DISTINCT comment_id
            FROM
            weshop_comment_picture
            WHERE
            comment_id = wc.id)
        </if>
        limit ${offset},${limit}
    </select>
    <select id="countIfRequirePictureList" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        weshop_comment wc
        where
        type_id=#{commentQuery.typeId}
        and value_id=#{commentQuery.valueId}
        <if test="commentQuery.requirePicture">
            AND wc.id IN (
            SELECT
            DISTINCT comment_id
            FROM
            weshop_comment_picture
            WHERE
            comment_id = wc.id)
        </if>
    </select>

    <select id="selectCommentStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as allCount,
            COUNT(CASE WHEN wc.id IN (
                SELECT DISTINCT comment_id FROM weshop_comment_picture WHERE comment_id = wc.id
            ) THEN 1 END) as hasPicCount,
            COUNT(CASE WHEN rating >= 4 THEN 1 END) as goodCount,
            COUNT(CASE WHEN rating = 3 THEN 1 END) as normalCount,
            COUNT(CASE WHEN rating &lt;= 2 THEN 1 END) as badCount,
            ROUND(AVG(CASE WHEN rating IS NOT NULL THEN rating END), 2) as averageRating,
            ROUND(
                CASE
                    WHEN COUNT(*) > 0 THEN (COUNT(CASE WHEN rating >= 4 THEN 1 END) * 100.0 / COUNT(*))
                    ELSE 100.0
                END,
                2
            ) as satisfactionRate
        FROM
            weshop_comment wc
        WHERE
            type_id = #{commentQuery.typeId}
            AND value_id = #{commentQuery.valueId}
    </select>
</mapper>
