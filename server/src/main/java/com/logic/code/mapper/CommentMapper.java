package com.logic.code.mapper;

import com.logic.code.entity.Comment;
import com.logic.code.model.query.CommentQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:05
 * @desc
 */
@Mapper
public interface CommentMapper extends CommonMapper<Comment> {
    List<Comment> selectIfRequirePictureList(@Param("commentQuery") CommentQuery commentQuery, @Param("offset") int offset, @Param("limit") int limit);

    Integer countIfRequirePictureList(@Param("commentQuery") CommentQuery commentQuery);

    /**
     * 获取评论统计信息，包括平均评分
     */
    Map<String, Object> selectCommentStats(@Param("commentQuery") CommentQuery commentQuery);
}
