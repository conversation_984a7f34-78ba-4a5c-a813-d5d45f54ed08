package com.logic.code.service;

import com.alibaba.fastjson.JSONObject;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Address;
import com.logic.code.entity.User;
import com.logic.code.entity.UserCoupon;
import com.logic.code.entity.goods.Goods;
import com.logic.code.entity.goods.Product;
import com.logic.code.entity.order.Cart;
import com.logic.code.mapper.CartMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.model.vo.*;
import io.jsonwebtoken.Claims;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:22
 * @desc
 */
@Service
@Slf4j
public class CartService extends BaseService<Cart> {

    @Resource
    private CartMapper cartMapper;

    @Resource
    private RegionService regionService;

    @Resource
    private GoodsService goodsService;
    @Resource
    private ProductService productService;

    @Resource
    private GoodsSpecificationService goodsSpecificationService;

    @Resource
    private AddressService addressService;

    @Resource
    private UserCouponService userCouponService;

    @Resource
    private UserService userService;

    @Resource
    private PointsService pointsService;

    @Resource
    private FreightService freightService;

    @Override
    protected CommonMapper<Cart> getMapper() {
        return cartMapper;
    }

    public int updateNumberById(Integer number, Integer id) {
        return cartMapper.updateNumberById(number, id);
    }


    public CartResultVO getCart() {
        User userInfo = JwtHelper.getUserInfo();
        Claims currentClaims = JwtHelper.getCurrentClaims();
        List<Cart> cartList = queryList(new Cart().setUserId(userInfo.getId()).setSessionId(currentClaims.getId()));
        CartResultVO.CartTotalVO cartTotalVO = new CartResultVO.CartTotalVO();

        Integer goodsCount = 0;
        BigDecimal goodsAmount = BigDecimal.ZERO;
        Integer checkedGoodsCount = 0;
        BigDecimal checkedGoodsAmount = BigDecimal.ZERO;
        for (Cart cart : cartList) {
            goodsCount += cart.getNumber();
            //goodsAmount = goodsAmount + retailPrice * number
            goodsAmount = goodsAmount.add(
                    cart.getRetailPrice().multiply(new BigDecimal(cart.getNumber()))
            );
            if (cart.getChecked()) {
                checkedGoodsCount += cart.getNumber();
                checkedGoodsAmount = checkedGoodsAmount.add(
                        cart.getRetailPrice().multiply(new BigDecimal(cart.getNumber())));
            }
        }

        cartTotalVO.setGoodsCount(goodsCount)
                .setGoodsAmount(goodsAmount)
                .setCheckedGoodsCount(checkedGoodsCount)
                .setCheckedGoodsAmount(checkedGoodsAmount);

        return new CartResultVO(cartList, cartTotalVO);
    }

    public void deleteCartGoods(CartGoodsDeleteVO deleteVO) {
        List<Integer> productIds = Arrays.stream(deleteVO.getProductIds().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        queryByCriteria(Criteria.of(Cart.class).fields(Cart::getId).andIn(Cart::getProductId, productIds)).stream()
                .map(Cart::getId)
                .forEach(cartId -> deleteById(cartId));
    }

    public void checkedCartGoods(CartCheckedVO cartCheckedVO) {
        List<Integer> productIds = Arrays.stream(cartCheckedVO.getProductIds().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        queryByCriteria(Criteria.of(Cart.class).fields(Cart::getId).andIn(Cart::getProductId, productIds)).stream()
                .map(Cart::getId)
                .forEach(cartId -> updateNotNull(new Cart().setChecked(cartCheckedVO.getChecked()).setId(cartId)));
    }

    @Transactional
    public void addGoodsToCart(CartParamVO cartParamDTO) {
        User userInfo = JwtHelper.getUserInfo();
        Claims currentClaims = JwtHelper.getCurrentClaims();
        Goods goods = goodsService.queryById(cartParamDTO.getGoodsId());
        if (goods == null || goods.getIsDelete()) {
            //商品已下架
            throw new WeshopWechatException(WeshopWechatResultStatus.GOODS_HAVE_BEEN_TAKEN_OFF_THE_SHELVES);
        }

        // 验证产品规格
        Product product = validateProductSpecification(cartParamDTO.getGoodsId(), cartParamDTO.getProductId());
        if (product == null || product.getGoodsNumber() < cartParamDTO.getNumber()) {
            //库存不足
            throw new WeshopWechatException(WeshopWechatResultStatus.UNDER_STOCK);
        }
        Cart cart = queryOne(new Cart()
                .setGoodsId(cartParamDTO.getGoodsId())
                .setProductId(cartParamDTO.getProductId())
                .setUserId(userInfo.getId())
        );
        if (cart == null) {
            // 判断购物车中是否存在此规格商品
            List<String> goodsSpecificationValueList = new LinkedList<>();
            if (product.getGoodsSpecificationIds() != null) {
                List<Integer> specificationIdList = Arrays.stream(product.getGoodsSpecificationIds().split("_"))
                        .filter(id -> id.length() > 0)
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                if (!specificationIdList.isEmpty()) {
                    goodsSpecificationValueList = goodsSpecificationService.queryValueByGoodsIdAndIdIn(cartParamDTO.getGoodsId(), specificationIdList);
                }
            }
            Cart cartData = new Cart()
                    .setGoodsId(cartParamDTO.getGoodsId())
                    .setProductId(cartParamDTO.getProductId())
                    .setGoodsSn(product.getGoodsSn())
                    .setGoodsName(goods.getName())
                    .setListPicUrl(goods.getListPicUrl())
                    .setNumber(cartParamDTO.getNumber().shortValue())
                    .setSessionId(currentClaims.getId())
                    .setUserId(userInfo.getId())
                    .setRetailPrice(product.getRetailPrice())
                    .setMarketPrice(product.getRetailPrice())
                    .setGoodsSpecificationNameValue(
                            goodsSpecificationValueList.stream()
                                    .collect(Collectors.joining(";"))
                    )
                    .setGoodsSpecificationIds(product.getGoodsSpecificationIds())
                    .setChecked(true);
            create(cartData);
        } else {
            // 如果已经存在购物车中，则数量增加
            if (product.getGoodsNumber() < (cartParamDTO.getNumber() + cart.getNumber())) {
                throw new WeshopWechatException(WeshopWechatResultStatus.UNDER_STOCK);
            }
            updateNotNull(new Cart().setNumber(Integer.valueOf(cartParamDTO.getNumber() + cart.getNumber()).shortValue()).setId(cart.getId()));
        }

    }

    @Transactional
    public void updateGoods(CartParamVO cartParamDTO) {
        User userInfo = JwtHelper.getUserInfo();
        Claims currentClaims = JwtHelper.getCurrentClaims();

        // 验证产品规格并获取库存信息
        Product product = validateProductSpecification(cartParamDTO.getGoodsId(), cartParamDTO.getProductId());
        if (product == null || product.getGoodsNumber() < cartParamDTO.getNumber()) {
            //库存不足
            throw new WeshopWechatException(WeshopWechatResultStatus.UNDER_STOCK);
        }
        // 判断是否已经存在product_id购物车商品
        Cart cart = queryById(cartParamDTO.getId());
        if (cart != null && cart.getProductId().equals(cartParamDTO.getProductId())) {
            // 只是更新number
            updateNotNull(new Cart()
                    .setNumber(cartParamDTO.getNumber().shortValue())
                    .setId(cartParamDTO.getId())
            );
            return;
        }
        Cart newCartInfo = queryOne(
                new Cart()
                        .setUserId(userInfo.getId())
                        .setSessionId(currentClaims.getId())
                        .setGoodsId(cartParamDTO.getGoodsId())
                        .setProductId(cartParamDTO.getProductId())
        );
        if (newCartInfo == null) {
            //直接更新原来的cartInfo
            // 判断购物车中是否存在此规格商品
            List<String> goodsSpecificationValueList = new LinkedList<>();
            if (product.getGoodsSpecificationIds() != null) {
                List<Integer> specificationIdList = Arrays.stream(product.getGoodsSpecificationIds()
                                .split("_"))
                        .filter(id -> id.length() > 0)
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                if (!specificationIdList.isEmpty()) {
                    goodsSpecificationValueList = goodsSpecificationService.queryValueByGoodsIdAndIdIn(cartParamDTO.getGoodsId(), specificationIdList);
                }
            }
            Cart cartData = new Cart()
                    .setId(cartParamDTO.getId())
                    .setNumber(cartParamDTO.getNumber().shortValue())
                    .setGoodsSpecificationNameValue(
                            goodsSpecificationValueList.stream()
                                    .collect(Collectors.joining(";"))
                    )
                    .setGoodsSpecificationIds(product.getGoodsSpecificationIds())
                    .setRetailPrice(product.getRetailPrice())
                    .setMarketPrice(product.getRetailPrice())
                    .setProductId(cartParamDTO.getProductId())
                    .setGoodsSn(product.getGoodsSn());
            updateNotNull(cartData);
        } else {
            // 合并购物车已有的product信息，删除已有的数据
            Integer newNumber = cartParamDTO.getNumber() + newCartInfo.getNumber();
            if (product == null || product.getGoodsNumber() < newNumber) {
                //库存不足
                throw new WeshopWechatException(WeshopWechatResultStatus.UNDER_STOCK);
            }
            deleteById(newCartInfo.getId());
            Cart cartData = new Cart()
                    .setId(cartParamDTO.getId())
                    .setNumber(newNumber.shortValue())
                    .setGoodsSpecificationNameValue(newCartInfo.getGoodsSpecificationNameValue())
                    .setGoodsSpecificationIds(newCartInfo.getGoodsSpecificationIds())
                    .setRetailPrice(product.getRetailPrice())
                    .setMarketPrice(product.getRetailPrice())
                    .setProductId(cartParamDTO.getProductId())
                    .setGoodsSn(product.getGoodsSn());
            updateNotNull(cartData);
        }


    }

    public CartCheckoutVO checkoutCart(String addressId, Integer couponId) {
        return checkoutCartWithPoints(addressId, couponId, null);
    }

    public CartCheckoutVO checkoutCartWithPoints(String addressId, Integer couponId, Integer usePoints) {
        User userInfo = JwtHelper.getUserInfo();
        CartCheckoutVO cartCheckoutDTO = new CartCheckoutVO();
        
        // 选择收货地址（复用原有逻辑）
        Address checkedAddress = selectAddress(addressId, userInfo.getId());
        CartCheckoutVO.CheckedAddressVO checkedAddressVO = null;
        if (checkedAddress != null) {
            checkedAddressVO = buildAddressVO(checkedAddress);
        }
        
        // 根据收货地址计算运费
        BigDecimal freightPrice = BigDecimal.ZERO;
        if (checkedAddress != null) {
            freightPrice = freightService.calculateFreight(checkedAddress, freightPrice);
        }

        CartResultVO cartData = this.getCart();
        List<Cart> checkedGoodsList = cartData.getCartList().stream()
                .filter(Cart::getChecked)
                .collect(Collectors.toList());

        // 获取用户信息
        User currentUser = userService.queryById(userInfo.getId());
        BigDecimal userBalance = currentUser.getBalance() != null ? currentUser.getBalance() : BigDecimal.ZERO;
        Integer userPoints = currentUser.getPoints() != null ? currentUser.getPoints() : 0;

        // 获取所有可用的优惠券
        List<UserCoupon> allUserCoupons = userCouponService.getUserCoupons(userInfo.getId(), "available", 1, 100);
        
        //计算订单的费用
        //商品总价
        BigDecimal goodsTotalPrice = cartData.getCartTotal().getCheckedGoodsAmount();
        
        // 筛选可用的优惠券（满足最低消费金额要求）
        List<UserCoupon> availableCoupons = filterAvailableCoupons(allUserCoupons, goodsTotalPrice);
        
        // 处理选中的优惠券
        UserCoupon selectedCoupon = null;
        BigDecimal couponPrice = BigDecimal.ZERO;
        if (couponId != null) {
            selectedCoupon = findCouponById(availableCoupons, couponId);
            if (selectedCoupon != null) {
                couponPrice = selectedCoupon.getAmount();
            }
        }
        
        // 计算订单总价（商品总价 + 运费 - 优惠券优惠）
        BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).subtract(couponPrice);
        
        // 获取积分配置
        com.logic.code.entity.PointsConfig pointsConfig = pointsService.getPointsConfig();
        
        // 计算积分相关信息
        Integer maxUsablePoints = pointsService.calculateMaxUsablePoints(userPoints, orderTotalPrice);
        Integer actualUsePoints = 0;
        BigDecimal pointsPrice = BigDecimal.ZERO;
        
        if (usePoints != null && usePoints > 0) {
            // 使用的积分不能超过最大可用积分
            actualUsePoints = Math.min(usePoints, maxUsablePoints);
            pointsPrice = pointsService.calculatePointsValue(actualUsePoints);
        }
        
        // 重新计算订单总价（扣除积分抵扣）
        orderTotalPrice = orderTotalPrice.subtract(pointsPrice);
        
        // 计算使用的余额金额（不能超过订单总价和用户余额）
        BigDecimal balancePrice = BigDecimal.ZERO;
        
        // 计算实际需要支付的金额（订单总价 - 使用的余额）
        BigDecimal actualPrice = orderTotalPrice.subtract(balancePrice);
        
        // 确保实际支付金额不为负数
        if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualPrice = BigDecimal.ZERO;
        }

        cartCheckoutDTO.setCheckedAddress(checkedAddressVO);
        cartCheckoutDTO.setFreightPrice(freightPrice);
        cartCheckoutDTO.setCheckedCoupon(selectedCoupon);
        cartCheckoutDTO.setCouponList(allUserCoupons); // 保持原有字段兼容性
        cartCheckoutDTO.setAvailableCoupons(availableCoupons); // 新增可用优惠券列表
        cartCheckoutDTO.setCouponPrice(couponPrice);
        cartCheckoutDTO.setUserBalance(userBalance);
        cartCheckoutDTO.setBalancePrice(balancePrice);
        cartCheckoutDTO.setUserPoints(userPoints);
        cartCheckoutDTO.setUsePoints(actualUsePoints);
        cartCheckoutDTO.setPointsPrice(pointsPrice);
        cartCheckoutDTO.setMaxUsablePoints(maxUsablePoints);
        cartCheckoutDTO.setPointsConfig(pointsConfig);
        cartCheckoutDTO.setCheckedGoodsList(checkedGoodsList);
        cartCheckoutDTO.setGoodsTotalPrice(goodsTotalPrice);
        cartCheckoutDTO.setOrderTotalPrice(orderTotalPrice);
        cartCheckoutDTO.setActualPrice(actualPrice);
        return cartCheckoutDTO;
    }


    /**
     * 筛选可用的优惠券（满足最低消费金额要求）
     */
    private List<UserCoupon> filterAvailableCoupons(List<UserCoupon> allCoupons, BigDecimal orderAmount) {
        return allCoupons.stream()
                .filter(coupon -> coupon.getMinAmount().compareTo(orderAmount) <= 0)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据ID查找优惠券
     */
    private UserCoupon findCouponById(List<UserCoupon> coupons, Integer couponId) {
        return coupons.stream()
                .filter(coupon -> coupon.getId().equals(couponId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 高级结算方法，支持余额、优惠券和积分组合使用
     */
    public CartCheckoutVO advancedCheckout(String addressId, Integer couponId, BigDecimal useBalance, Integer usePoints) {
        User userInfo = JwtHelper.getUserInfo();
        CartCheckoutVO cartCheckoutDTO = new CartCheckoutVO();
        
        // 选择收货地址（复用原有逻辑）
        Address checkedAddress = selectAddress(addressId, userInfo.getId());
        CartCheckoutVO.CheckedAddressVO checkedAddressVO = null;
        if (checkedAddress != null) {
            checkedAddressVO = buildAddressVO(checkedAddress);
        }
        
        // 计算运费
        BigDecimal freightPrice = BigDecimal.ZERO;
        if (checkedAddress != null) {
            freightPrice = freightService.calculateFreight(checkedAddress,freightPrice );
        }
        
        // 获取购物车数据
        CartResultVO cartData = this.getCart();
        List<Cart> checkedGoodsList = cartData.getCartList().stream()
                .filter(Cart::getChecked)
                .collect(Collectors.toList());
        
        // 获取用户信息
        User currentUser = userService.queryById(userInfo.getId());
        BigDecimal userBalance = currentUser.getBalance() != null ? currentUser.getBalance() : BigDecimal.ZERO;
        Integer userPoints = currentUser.getPoints() != null ? currentUser.getPoints() : 0;
        
        // 获取所有可用的优惠券
        List<UserCoupon> allUserCoupons = userCouponService.getUserCoupons(userInfo.getId(), "available", 1, 100);
        
        // 计算商品总价
        BigDecimal goodsTotalPrice = cartData.getCartTotal().getCheckedGoodsAmount();
        
        // 筛选可用的优惠券
        List<UserCoupon> availableCoupons = filterAvailableCoupons(allUserCoupons, goodsTotalPrice);
        
        // 处理选中的优惠券
        UserCoupon selectedCoupon = null;
        BigDecimal couponPrice = BigDecimal.ZERO;
        if (couponId != null && couponId > 0) {
            log.info("查找优惠券ID: {}, 可用优惠券数量: {}", couponId, availableCoupons.size());
            selectedCoupon = findCouponById(availableCoupons, couponId);
            if (selectedCoupon != null) {
                couponPrice = selectedCoupon.getAmount();
                log.info("找到优惠券: {}, 抵扣金额: {}", selectedCoupon.getTitle(), couponPrice);
            } else {
                log.warn("未找到优惠券ID: {}", couponId);
            }
        } else {
            log.info("未选择优惠券，couponId: {}", couponId);
        }
        
        // 计算订单总价（商品总价 + 运费 - 优惠券优惠）
        BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).subtract(couponPrice);
        
        // 获取积分配置
        com.logic.code.entity.PointsConfig pointsConfig = pointsService.getPointsConfig();
        
        // 计算积分相关信息
        Integer maxUsablePoints = pointsService.calculateMaxUsablePoints(userPoints, orderTotalPrice);
        Integer actualUsePoints = 0;
        BigDecimal pointsPrice = BigDecimal.ZERO;
        
        if (usePoints != null && usePoints > 0) {
            // 使用的积分不能超过最大可用积分
            actualUsePoints = Math.min(usePoints, maxUsablePoints);
            pointsPrice = pointsService.calculatePointsValue(actualUsePoints);
        }
        
        // 重新计算订单总价（扣除积分抵扣）
        orderTotalPrice = orderTotalPrice.subtract(pointsPrice);
        
        // 处理余额使用
        BigDecimal balancePrice = BigDecimal.ZERO;
        if (useBalance != null && useBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 使用的余额不能超过用户余额和订单总价
            balancePrice = useBalance.min(userBalance).min(orderTotalPrice);
        }
        
        // 计算实际需要支付的金额
        BigDecimal actualPrice = orderTotalPrice.subtract(balancePrice);
        if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualPrice = BigDecimal.ZERO;
        }
        
        // 设置返回数据
        cartCheckoutDTO.setCheckedAddress(checkedAddressVO);
        cartCheckoutDTO.setFreightPrice(freightPrice);
        cartCheckoutDTO.setCheckedCoupon(selectedCoupon);
        cartCheckoutDTO.setCouponList(allUserCoupons);
        cartCheckoutDTO.setAvailableCoupons(availableCoupons);
        cartCheckoutDTO.setCouponPrice(couponPrice);
        cartCheckoutDTO.setUserBalance(userBalance);
        cartCheckoutDTO.setBalancePrice(balancePrice);
        cartCheckoutDTO.setUserPoints(userPoints);
        cartCheckoutDTO.setUsePoints(actualUsePoints);
        cartCheckoutDTO.setPointsPrice(pointsPrice);
        cartCheckoutDTO.setMaxUsablePoints(maxUsablePoints);
        cartCheckoutDTO.setPointsConfig(pointsConfig);
        cartCheckoutDTO.setCheckedGoodsList(checkedGoodsList);
        cartCheckoutDTO.setGoodsTotalPrice(goodsTotalPrice);
        cartCheckoutDTO.setOrderTotalPrice(orderTotalPrice);
        cartCheckoutDTO.setActualPrice(actualPrice);
        
        return cartCheckoutDTO;
    }
    
    /**
     * 选择收货地址
     */
    private Address selectAddress(String addressId, Integer userId) {
        Address checkedAddress = null;
        if (addressId != null) {
            checkedAddress = addressService.queryOne(new Address()
                    .setId(Integer.valueOf(addressId))
                    .setUserId(userId)
            );
        }
        if (checkedAddress == null) {
            List<Address> list = addressService.queryList(new Address().setUserId(userId).setIsDefault(true));
            if (ListUtils.isNotBlank(list)) {
                checkedAddress = list.get(0);
            }
            if (checkedAddress == null) {
                List<Address> addresses = addressService.queryList(new Address().setUserId(userId));
                if (ListUtils.isNotBlank(addresses)) {
                    checkedAddress = addresses.get(0);
                }
            }
        }
        return checkedAddress;
    }
    
    /**
     * 构建地址VO
     */
    private CartCheckoutVO.CheckedAddressVO buildAddressVO(Address address) {
        CartCheckoutVO.CheckedAddressVO addressVO = new CartCheckoutVO.CheckedAddressVO(address)
                .setProvinceName(regionService.queryNameById(address.getProvinceId()))
                .setCityName(regionService.queryNameById(address.getCityId()))
                .setDistrictName(regionService.queryNameById(address.getDistrictId()));
        
        addressVO.setFullRegion(
                addressVO.getProvinceName() + addressVO.getCityName() + addressVO.getDistrictName()
        );
        
        return addressVO;
    }

    public List<Cart> packageGoodsToCart(CartParamVO cartParamDTO) {
        log.info("packageGoodsToCart:{}", JSONObject.toJSONString(cartParamDTO));
        User userInfo = JwtHelper.getUserInfo();
        Claims currentClaims = JwtHelper.getCurrentClaims();
        Goods goods = goodsService.queryById(cartParamDTO.getGoodsId());
        if (goods == null || goods.getIsDelete()) {
            //商品已下架
            throw new WeshopWechatException(WeshopWechatResultStatus.GOODS_HAVE_BEEN_TAKEN_OFF_THE_SHELVES);
        }

        // 验证产品规格，如果没有指定productId则获取默认产品
        Product product = validateProductSpecification(cartParamDTO.getGoodsId(), cartParamDTO.getProductId());
        if (product == null || product.getGoodsNumber() < cartParamDTO.getNumber()) {
            //库存不足
            throw new WeshopWechatException(WeshopWechatResultStatus.UNDER_STOCK);
        }

        // 如果没有指定productId，使用找到的产品ID
        if (cartParamDTO.getProductId() == null || cartParamDTO.getProductId() <= 0) {
            cartParamDTO.setProductId(product.getId());
        }

        // 判断购物车中是否存在此规格商品
        List<String> goodsSpecificationValueList = new LinkedList<>();
        if (product.getGoodsSpecificationIds() != null) {
            List<Integer> specificationIdList = Arrays.stream(product.getGoodsSpecificationIds().split("_"))
                    .filter(id -> id.length() > 0)
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            if (!specificationIdList.isEmpty()) {
                goodsSpecificationValueList = goodsSpecificationService.queryValueByGoodsIdAndIdIn(cartParamDTO.getGoodsId(), specificationIdList);
            }
        }
        Cart cartData = new Cart()
                .setGoodsId(cartParamDTO.getGoodsId())
                .setProductId(cartParamDTO.getProductId())
                .setGoodsSn(product.getGoodsSn())
                .setGoodsName(goods.getName())
                .setListPicUrl(goods.getListPicUrl())
                .setNumber(cartParamDTO.getNumber().shortValue())
                .setSessionId(currentClaims.getId())
                .setUserId(userInfo.getId())
                .setRetailPrice(product.getRetailPrice())
                .setMarketPrice(product.getRetailPrice())
                .setGoodsSpecificationNameValue(
                        goodsSpecificationValueList.stream()
                                .collect(Collectors.joining(";"))
                )
                .setGoodsSpecificationIds(product.getGoodsSpecificationIds())
                .setChecked(true);
        return Arrays.asList(cartData);

    }

    /**
     * 验证产品规格是否有效
     * @param goodsId 商品ID
     * @param productId 产品ID
     * @return 验证通过的产品信息
     */
    private Product validateProductSpecification(Integer goodsId, Integer productId) {
        if (productId == null || productId <= 0) {
            // 如果没有指定productId，查找该商品的默认产品
            Product productQuery = new Product();
            productQuery.setGoodsId(goodsId);
            List<Product> products = productService.queryList(productQuery);
            if (products.isEmpty()) {
                throw new WeshopWechatException(WeshopWechatResultStatus.GOODS_HAVE_BEEN_TAKEN_OFF_THE_SHELVES);
            }
            return products.get(0);
        }

        // 验证指定的productId是否属于该商品
        Product productQuery = new Product();
        productQuery.setGoodsId(goodsId);
        productQuery.setId(productId);
        Product product = productService.queryOne(productQuery);

        if (product == null) {
            throw new WeshopWechatException(WeshopWechatResultStatus.GOODS_HAVE_BEEN_TAKEN_OFF_THE_SHELVES);
        }

        return product;
    }

}
