# 特定地区运费加收功能实现说明

## 功能概述
为收货地址为港、澳、台、新疆、西藏、海南的订单统一加收5元快递费。

## 实现方案

### 1. 创建运费计算服务
创建了 `FreightService` 类，负责根据收货地址计算运费：

**文件位置**: `server/src/main/java/com/logic/code/service/FreightService.java`

**主要功能**:
- 根据收货地址的省份判断是否为特殊地区
- 特殊地区（港、澳、台、新疆、西藏、海南）加收5元运费
- 其他地区免运费

**特殊地区列表**:
```java
private static final List<String> SPECIAL_REGIONS = Arrays.asList(
    "香港", "澳门", "台湾", "新疆", "西藏", "海南"
);
```

**运费金额**:
```java
private static final BigDecimal SPECIAL_REGION_FREIGHT = new BigDecimal("5.00");
```

### 2. 修改购物车服务
在 `CartService` 中集成运费计算功能：

**修改位置**: `server/src/main/java/com/logic/code/service/CartService.java`

**主要修改**:
1. 注入 `FreightService` 依赖
2. 在 `checkoutCartWithPoints` 方法中调用运费计算
3. 在 `advancedCheckout` 方法中调用运费计算

**修改代码**:
```java
// 根据收货地址计算运费
BigDecimal freightPrice = BigDecimal.ZERO;
if (checkedAddress != null) {
    freightPrice = freightService.calculateFreight(checkedAddress, goodsTotalPrice);
}
```

### 3. 修改订单服务
在 `OrderService` 中集成运费计算功能：

**修改位置**: `server/src/main/java/com/logic/code/service/OrderService.java`

**主要修改**:
1. 注入 `FreightService` 依赖
2. 在 `submitOrder` 方法中调用运费计算
3. 在订单创建时设置正确的运费金额

**修改代码**:
```java
// 运费价格
BigDecimal freightPrice = freightService.calculateFreight(checkedAddress, goodsTotalPrice);

// 设置订单运费
orderInfo.setFreightPrice(freightPrice);
```

### 4. 前端显示
前端结算页面已经支持运费显示：

**模板位置**: `app/wjhx/pages/shopping/checkout/checkout.wxml`
```xml
<view class="price-item">
    <text class="price-label">运费</text>
    <text class="price-value">¥{{freightPrice}}</text>
</view>
```

## 价格计算公式
```
实际支付金额 = 商品总价 + 运费 - 优惠券抵扣 - 积分抵扣 - 余额抵扣
```

其中：
- **运费**: 特殊地区5元，其他地区0元
- **特殊地区**: 港、澳、台、新疆、西藏、海南

## 地区判断逻辑
1. 通过收货地址的 `provinceId` 获取省份名称
2. 判断省份名称是否在特殊地区列表中
3. 支持精确匹配和包含匹配，确保各种省份名称变体都能正确识别

## 测试场景

### 测试用例1：特殊地区订单
1. 选择收货地址为"新疆维吾尔自治区"
2. 添加商品到购物车，进入结算页面
3. 验证：
   - 运费显示：¥5.00
   - 订单总价 = 商品价格 + 5元运费
   - 实付金额正确计算

### 测试用例2：普通地区订单
1. 选择收货地址为"北京市"
2. 添加商品到购物车，进入结算页面
3. 验证：
   - 运费显示：¥0.00
   - 订单总价 = 商品价格
   - 实付金额正确计算

### 测试用例3：特殊地区+优惠券
1. 选择收货地址为"香港特别行政区"
2. 添加商品，选择优惠券
3. 验证：
   - 运费显示：¥5.00
   - 实付金额 = 商品价格 + 5元运费 - 优惠券抵扣

## 注意事项
1. 运费计算基于省份名称，确保地区数据库中的省份名称正确
2. 特殊地区列表可以根据业务需求调整
3. 运费金额可以通过配置文件或数据库进行动态配置
4. 前端页面已经支持运费显示，无需额外修改

## 部署说明
1. 确保新增的 `FreightService` 类被正确编译
2. 重启应用服务器
3. 测试各种地区的运费计算是否正确
4. 验证订单创建和支付流程正常

## 扩展性
该实现具有良好的扩展性：
1. 可以轻松添加更多特殊地区
2. 可以为不同地区设置不同的运费标准
3. 可以根据商品重量、体积等因素计算运费
4. 可以集成第三方物流API进行实时运费计算