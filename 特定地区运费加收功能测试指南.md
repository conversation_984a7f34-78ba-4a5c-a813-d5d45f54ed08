# 特定地区运费加收功能测试指南

## 测试目标
验证收货地址为港、澳、台、新疆、西藏、海南的订单能够正确加收5元快递费。

## 测试环境准备

### 1. 数据准备
确保系统中有以下测试数据：
- 测试用户账号
- 不同地区的收货地址（包括特殊地区和普通地区）
- 测试商品
- 测试优惠券（可选）

### 2. 地址数据
需要创建以下测试地址：

**特殊地区地址**：
- 香港特别行政区
- 澳门特别行政区  
- 台湾省
- 新疆维吾尔自治区
- 西藏自治区
- 海南省

**普通地区地址**：
- 北京市
- 上海市
- 广东省深圳市
- 江苏省南京市

## 详细测试用例

### 测试用例1：新疆地区运费计算
**测试步骤**：
1. 登录测试账号
2. 添加商品到购物车（如：价格100元的商品）
3. 进入结算页面
4. 选择收货地址为"新疆维吾尔自治区乌鲁木齐市"
5. 查看价格明细

**预期结果**：
- 商品总价：¥100.00
- 运费：¥5.00
- 订单总价：¥105.00
- 实付金额：¥105.00

### 测试用例2：香港地区运费计算
**测试步骤**：
1. 选择收货地址为"香港特别行政区"
2. 添加商品到购物车（价格50元）
3. 进入结算页面

**预期结果**：
- 商品总价：¥50.00
- 运费：¥5.00
- 订单总价：¥55.00
- 实付金额：¥55.00

### 测试用例3：普通地区运费计算
**测试步骤**：
1. 选择收货地址为"北京市朝阳区"
2. 添加商品到购物车（价格100元）
3. 进入结算页面

**预期结果**：
- 商品总价：¥100.00
- 运费：¥0.00
- 订单总价：¥100.00
- 实付金额：¥100.00

### 测试用例4：特殊地区+优惠券
**测试步骤**：
1. 选择收货地址为"西藏自治区拉萨市"
2. 添加商品到购物车（价格200元）
3. 选择10元优惠券
4. 进入结算页面

**预期结果**：
- 商品总价：¥200.00
- 运费：¥5.00
- 优惠券抵扣：-¥10.00
- 订单总价：¥195.00
- 实付金额：¥195.00

### 测试用例5：特殊地区+积分抵扣
**测试步骤**：
1. 选择收货地址为"海南省海口市"
2. 添加商品到购物车（价格150元）
3. 使用1000积分抵扣（假设100积分=1元）
4. 进入结算页面

**预期结果**：
- 商品总价：¥150.00
- 运费：¥5.00
- 积分抵扣：-¥10.00
- 订单总价：¥145.00
- 实付金额：¥145.00

### 测试用例6：特殊地区+余额抵扣
**测试步骤**：
1. 选择收货地址为"台湾省台北市"
2. 添加商品到购物车（价格80元）
3. 使用20元余额抵扣
4. 进入结算页面

**预期结果**：
- 商品总价：¥80.00
- 运费：¥5.00
- 余额抵扣：-¥20.00
- 订单总价：¥65.00
- 实付金额：¥65.00

### 测试用例7：组合抵扣测试
**测试步骤**：
1. 选择收货地址为"澳门特别行政区"
2. 添加商品到购物车（价格300元）
3. 使用50元优惠券
4. 使用2000积分抵扣（20元）
5. 使用30元余额抵扣

**预期结果**：
- 商品总价：¥300.00
- 运费：¥5.00
- 优惠券抵扣：-¥50.00
- 积分抵扣：-¥20.00
- 余额抵扣：-¥30.00
- 订单总价：¥205.00
- 实付金额：¥205.00

## 订单提交测试

### 测试用例8：特殊地区订单提交
**测试步骤**：
1. 完成测试用例1的步骤
2. 点击"提交订单"
3. 完成支付流程
4. 查看订单详情

**预期结果**：
- 订单创建成功
- 订单详情中运费显示¥5.00
- 支付金额正确（¥105.00）
- 订单状态正常

### 测试用例9：普通地区订单提交
**测试步骤**：
1. 完成测试用例3的步骤
2. 点击"提交订单"
3. 完成支付流程
4. 查看订单详情

**预期结果**：
- 订单创建成功
- 订单详情中运费显示¥0.00
- 支付金额正确（¥100.00）
- 订单状态正常

## 边界情况测试

### 测试用例10：地址切换测试
**测试步骤**：
1. 进入结算页面，选择普通地区地址
2. 记录运费金额（应为¥0.00）
3. 切换到特殊地区地址
4. 观察运费变化

**预期结果**：
- 地址切换后运费自动更新为¥5.00
- 订单总价重新计算
- 页面显示无异常

### 测试用例11：无地址情况
**测试步骤**：
1. 删除所有收货地址
2. 进入结算页面

**预期结果**：
- 提示添加收货地址
- 运费显示¥0.00或不显示
- 无法提交订单

## 性能测试

### 测试用例12：并发订单测试
**测试步骤**：
1. 模拟多个用户同时下单
2. 部分用户选择特殊地区地址
3. 部分用户选择普通地区地址

**预期结果**：
- 所有订单运费计算正确
- 系统响应正常
- 无数据错乱

## 回归测试

### 测试用例13：原有功能验证
**测试步骤**：
1. 测试优惠券功能是否正常
2. 测试积分抵扣功能是否正常
3. 测试余额抵扣功能是否正常
4. 测试订单流程是否正常

**预期结果**：
- 所有原有功能正常工作
- 运费计算不影响其他功能
- 用户体验良好

## 测试检查点

### 前端检查点
- [ ] 结算页面运费显示正确
- [ ] 价格明细计算准确
- [ ] 地址切换时运费实时更新
- [ ] 页面样式显示正常
- [ ] 交互体验流畅

### 后端检查点
- [ ] 运费计算逻辑正确
- [ ] 订单数据保存完整
- [ ] 地区判断准确
- [ ] 价格计算无误
- [ ] 日志记录完整

### 数据库检查点
- [ ] 订单表运费字段正确
- [ ] 订单商品表数据完整
- [ ] 地址表数据正确
- [ ] 无数据丢失或错误

## 测试报告模板

### 测试结果记录
| 测试用例 | 执行结果 | 实际运费 | 预期运费 | 备注 |
|---------|---------|---------|---------|------|
| 新疆地区 | ✅/❌ | ¥5.00 | ¥5.00 | |
| 香港地区 | ✅/❌ | ¥5.00 | ¥5.00 | |
| 普通地区 | ✅/❌ | ¥0.00 | ¥0.00 | |

### 问题记录
| 问题描述 | 严重程度 | 复现步骤 | 解决方案 |
|---------|---------|---------|---------|
| | 高/中/低 | | |

## 测试完成标准
- [ ] 所有测试用例执行完毕
- [ ] 特殊地区运费计算100%正确
- [ ] 普通地区运费计算100%正确
- [ ] 订单提交流程正常
- [ ] 原有功能无回归问题
- [ ] 性能表现符合要求
- [ ] 用户体验良好