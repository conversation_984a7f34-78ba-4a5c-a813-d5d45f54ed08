# 特定地区运费加收功能验证

## 快速验证步骤

### 1. 后端验证
执行测试SQL脚本创建测试数据：
```sql
-- 执行测试数据脚本
source database/test_freight_calculation.sql;
```

### 2. 前端验证

#### 步骤1：登录测试账号
- 用户名：test_freight_user
- 密码：test123

#### 步骤2：添加商品到购物车
- 选择"运费测试商品"（价格¥100.00）
- 数量：1件

#### 步骤3：进入结算页面
点击"立即结算"进入订单确认页面

#### 步骤4：测试特殊地区运费
依次选择以下地址，验证运费计算：

**新疆地址**：
- 收货人：张三
- 地址：新疆维吾尔自治区乌鲁木齐市天山区解放南路123号
- 预期运费：¥5.00
- 预期总价：¥105.00

**西藏地址**：
- 收货人：李四  
- 地址：西藏自治区拉萨市城关区北京中路456号
- 预期运费：¥5.00
- 预期总价：¥105.00

**海南地址**：
- 收货人：王五
- 地址：海南省海口市龙华区国贸大道789号
- 预期运费：¥5.00
- 预期总价：¥105.00

**香港地址**：
- 收货人：陈六
- 地址：香港特别行政区香港岛中环皇后大道中100号
- 预期运费：¥5.00
- 预期总价：¥105.00

**澳门地址**：
- 收货人：赵七
- 地址：澳门特别行政区澳门半岛新马路200号
- 预期运费：¥5.00
- 预期总价：¥105.00

**台湾地址**：
- 收货人：孙八
- 地址：台湾省台北市信义区市府路300号
- 预期运费：¥5.00
- 预期总价：¥105.00

#### 步骤5：测试普通地区运费
选择以下地址，验证运费为0：

**北京地址**：
- 收货人：普通用户1
- 地址：北京市朝阳区建国门外大街400号
- 预期运费：¥0.00
- 预期总价：¥100.00

**上海地址**：
- 收货人：普通用户2
- 地址：上海市浦东新区陆家嘴环路500号
- 预期运费：¥0.00
- 预期总价：¥100.00

**广东地址**：
- 收货人：普通用户3
- 地址：广东省深圳市南山区深南大道600号
- 预期运费：¥0.00
- 预期总价：¥100.00

### 3. 验证检查点

#### 前端检查点
- [ ] 结算页面运费显示正确
- [ ] 切换地址时运费实时更新
- [ ] 价格明细计算准确
- [ ] 页面无JavaScript错误
- [ ] 用户体验流畅

#### 后端检查点
- [ ] FreightService.calculateFreight()方法正常工作
- [ ] 地区判断逻辑正确
- [ ] 运费金额计算准确
- [ ] 日志输出正常
- [ ] 无异常抛出

### 4. 组合功能验证

#### 验证1：特殊地区+优惠券
1. 选择新疆地址
2. 使用10元优惠券
3. 验证：商品¥100 + 运费¥5 - 优惠券¥10 = ¥95

#### 验证2：特殊地区+积分抵扣
1. 选择香港地址
2. 使用1000积分（¥10）
3. 验证：商品¥100 + 运费¥5 - 积分¥10 = ¥95

#### 验证3：特殊地区+余额抵扣
1. 选择西藏地址
2. 使用¥20余额
3. 验证：商品¥100 + 运费¥5 - 余额¥20 = ¥85

### 5. 订单提交验证

#### 验证步骤
1. 选择特殊地区地址（如新疆）
2. 点击"提交订单"
3. 完成支付流程
4. 查看订单详情

#### 验证点
- [ ] 订单创建成功
- [ ] 订单中运费字段为¥5.00
- [ ] 支付金额正确
- [ ] 订单状态正常

### 6. 数据库验证

#### 验证SQL
```sql
-- 查看最新订单的运费信息
SELECT 
    id,
    order_sn,
    goods_price,
    freight_price,
    actual_price,
    province,
    (SELECT name FROM weshop_region WHERE id = province) as province_name
FROM weshop_order 
WHERE user_id = 9999 
ORDER BY create_time DESC 
LIMIT 5;
```

#### 验证点
- [ ] freight_price字段值正确
- [ ] actual_price计算正确
- [ ] 省份信息正确

### 7. 性能验证

#### 验证方法
1. 使用浏览器开发者工具监控网络请求
2. 观察运费计算的响应时间
3. 检查是否有不必要的重复请求

#### 验证标准
- [ ] 运费计算响应时间 < 500ms
- [ ] 地址切换时无重复请求
- [ ] 内存使用正常

### 8. 错误处理验证

#### 验证场景
1. 地址信息不完整
2. 省份ID不存在
3. 网络异常情况

#### 验证点
- [ ] 错误情况下有合理的默认值
- [ ] 用户界面显示友好的错误信息
- [ ] 系统不会崩溃

### 9. 兼容性验证

#### 验证环境
- [ ] 微信小程序
- [ ] iOS设备
- [ ] Android设备
- [ ] 不同网络环境

### 10. 验证报告

#### 成功标准
- 所有特殊地区运费计算为¥5.00
- 所有普通地区运费计算为¥0.00
- 订单提交流程正常
- 数据库记录正确
- 用户体验良好

#### 问题记录
| 问题 | 严重程度 | 状态 | 备注 |
|------|---------|------|------|
|      |         |      |      |

#### 验证结论
- [ ] 功能正常，可以上线
- [ ] 存在问题，需要修复
- [ ] 需要进一步测试

### 11. 清理测试数据

验证完成后，可以执行以下SQL清理测试数据：
```sql
-- 清理测试订单
DELETE FROM weshop_order WHERE user_id = 9999;

-- 清理测试地址
DELETE FROM weshop_address WHERE user_id = 9999;

-- 清理测试用户
DELETE FROM weshop_user WHERE id = 9999;

-- 清理测试商品
DELETE FROM weshop_goods WHERE id = 9999;
DELETE FROM weshop_product WHERE id = 9999;

-- 清理测试优惠券
DELETE FROM weshop_coupon_config WHERE id = 9999;
DELETE FROM weshop_user_coupon WHERE id = 9999;
```