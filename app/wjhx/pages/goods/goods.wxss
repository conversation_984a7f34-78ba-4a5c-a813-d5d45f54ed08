page {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  margin-bottom: 120rpx;
  background: transparent;
}

.goodsimgs {
  width: 100%;
  height: 750rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.goodsimgs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.02) 0%, rgba(25, 118, 210, 0.02) 100%);
  pointer-events: none;
  z-index: 1;
}

.goodsimgs image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  object-position: center;
  transition: transform 200ms ease;
}

.goodsimgs swiper-item:active image {
  transform: scale(1.02);
}

/* 会员日小提示样式 */
.member-day-tip {
  width: 100%;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.member-day-tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.tip-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.tip-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  animation: slideText 8s infinite linear;
  white-space: nowrap;
  letter-spacing: 1rpx;
}

@keyframes slideText {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

.service-policy {
  width: 100%;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.service-policy .item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(66, 165, 245, 0.08);
  border-radius: 20rpx;
  border: 1rpx solid rgba(66, 165, 245, 0.15);
  transition: all 200ms ease;
  position: relative;
  overflow: hidden;
}

.service-policy .item::before {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background: #42A5F5;
  border-radius: 50%;
}

.service-policy .item {
  padding-left: 32rpx;
}

.service-policy .item:active {
  background: rgba(66, 165, 245, 0.12);
  transform: scale(0.98);
}

.goods-info {
  width: 100%;
  background: #ffffff;
  margin: 20rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.goods-info .c {
  display: block;
  width: 100%;
  padding: 40rpx 36rpx;
  box-sizing: border-box;
}

.goods-info .c text {
  display: block;
  width: 100%;
  text-align: left;
}

.goods-info .name {
  font-size: 36rpx;
  line-height: 1.4;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}

.goods-info .desc {
  font-size: 28rpx;
  line-height: 1.5;
  color: #64748b;
  margin-bottom: 24rpx;
  font-weight: 400;
}

/* 确保内联样式不被覆盖 */
.goods-info .desc  {
  /* 允许内联样式覆盖 */
}

/* 富文本内容样式 */
.goods-info .desc b,
.goods-info .desc strong {
  font-weight: 700;
  color: #1e293b;
}

.goods-info .desc i,
.goods-info .desc em {
  font-style: italic;
}

.goods-info .desc u {
  text-decoration: underline;
}

.goods-info .desc div {
  margin-bottom: 8rpx;
}

.goods-info .desc img {
  max-width: 100%;
  height: auto;
  border-radius: 8rpx;
  margin: 8rpx 0;
}

/* 支持 span 标签的内联样式 */
.goods-info .desc span {
  /* 继承父元素样式，允许内联样式覆盖 */
}

/* 高亮文本样式 - 加粗红色 */
.goods-info .desc .highlight-text {
  color: #ef4444;
  font-weight: 700;
}

.goods-info .desc .highlight-green-text {
  color: #4ced55;
  font-weight: 700;
}

.goods-info .price {
  font-size: 42rpx;
  line-height: 1.2;
  color: #ef4444;
  font-weight: 700;
  margin-top: 15rpx;
  margin-bottom: 15rpx;
  letter-spacing: 0.5rpx;
  position: relative;
}

.goods-info .price::before {
  content: '￥';
  font-size: 28rpx;
  margin-right: 4rpx;
  font-weight: 600;
}

.goods-info .stock {
  font-size: 26rpx;
  line-height: 1.3;
  color: #64748b;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(100, 116, 139, 0.08);
  border-radius: 16rpx;
  display: inline-block;
}

.goods-info .brand {
  margin-top: 20rpx;
  text-align: left;
}

.goods-info .brand text {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 600;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.25);
  transition: all 200ms ease;
  position: relative;
  overflow: hidden;
}

.goods-info .brand text::after {
  content: '→';
  margin-left: 8rpx;
  font-size: 20rpx;
  transition: transform 200ms ease;
}

.goods-info .brand text:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.3);
}

.goods-info .brand text:active::after {
  transform: translateX(4rpx);
}

/* 规格选择器 */
.spec-selector {
  width: 100%;
  background: #ffffff;
  margin: 20rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  padding: 32rpx 36rpx;
  position: relative;
}

.spec-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.02) 0%, rgba(25, 118, 210, 0.02) 100%);
  opacity: 0;
  transition: opacity 200ms ease;
  pointer-events: none;
}

.spec-selector:active::before {
  opacity: 1;
}

.spec-selector:active {
  transform: scale(0.99);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.spec-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.spec-label {
  font-size: 32rpx;
  color: #06b6d4;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  position: relative;
  padding-left: 20rpx;
}

.spec-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 16rpx;
  background: linear-gradient(135deg, #06b6d4 0%, #16a34a 100%);
  border-radius: 3rpx;
}

.spec-text {
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1.4;
  transition: color 200ms ease;
}

.spec-text.placeholder {
  color: #94a3b8;
  font-weight: 500;
}

.spec-text.selected {
  color: #1e293b;
  position: relative;
}

.spec-text.selected::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 6rpx;
  background: #42A5F5;
  border-radius: 50%;
}

.spec-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: rgba(66, 165, 245, 0.08);
  border-radius: 50%;
  transition: all 200ms ease;
}

.spec-selector:active .spec-arrow {
  background: rgba(66, 165, 245, 0.15);
  transform: scale(0.95);
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
  transition: all 200ms ease;
}

.spec-selector:active .arrow-icon {
  opacity: 1;
  transform: translateX(2rpx);
}

/* 保持原有section-nav样式用于其他地方 */
.section-nav {
  width: 100%;
  background: #ffffff;
  margin: 20rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 200ms ease;
}

.section-nav:active {
  transform: scale(0.99);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.section-nav .t {
  float: left;
  width: calc(100% - 80rpx);
  padding: 32rpx 36rpx;
  font-size: 30rpx;
  color: #1e293b;
  font-weight: 500;
  line-height: 1.4;
  box-sizing: border-box;
}

.section-nav .i {
  float: right;
  width: 48rpx;
  height: 48rpx;
  margin: 32rpx 36rpx 32rpx 0;
  opacity: 0.6;
  transition: all 200ms ease;
}

.section-nav:active .i {
  opacity: 1;
  transform: translateX(-4rpx);
}

.section-act .t {
  float: left;
  display: flex;
  align-items: center;
  width: calc(100% - 80rpx);
  padding: 32rpx 36rpx;
  overflow: hidden;
  font-size: 30rpx;
  color: #64748b;
  font-weight: 500;
  box-sizing: border-box;
}

.section-act .label {
  color: #64748b;
  margin-right: 12rpx;
}

.section-act .tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  height: auto;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  margin: 0 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.2);
  transition: all 200ms ease;
}

.section-act .tag:active {
  transform: scale(0.95);
}

.section-act .text {
  display: inline-flex;
  align-items: center;
  height: auto;
  color: #f59e0b;
  font-size: 28rpx;
  font-weight: 600;
  margin-left: 8rpx;
}

.comments {
  width: 100%;
  background: #ffffff;
  margin: 20rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.comments .h {
  padding: 32rpx 36rpx;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 200ms ease;
}

.comments .h:active {
  background: rgba(248, 250, 252, 0.8);
}

.comments .h .t {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.comments .h .i {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  transition: all 200ms ease;
}

.comments .h .i::after {
  content: '→';
  margin-left: 8rpx;
  font-size: 24rpx;
  transition: transform 200ms ease;
}

.comments .h:active .i {
  color: #42A5F5;
}

.comments .h:active .i::after {
  transform: translateX(4rpx);
}

.comments .b {
  padding: 0 36rpx 36rpx 36rpx;
}

.comments .item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.6);
}

.comments .item:last-child {
  border-bottom: none;
}

.comments .info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.comments .user {
  display: flex;
  align-items: center;
  flex: 1;
}

.comments .user image {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid rgba(66, 165, 245, 0.1);
}

.comments .user text {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 500;
  line-height: 1.4;
}

.comments .time {
  font-size: 24rpx;
  color: #94a3b8;
  font-weight: 400;
}

.comments .content {
  line-height: 1.6;
  font-size: 28rpx;
  color: #475569;
  margin-bottom: 16rpx;
  font-weight: 400;
}

.comments .imgs {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.comments .imgs .img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all 200ms ease;
}

.comments .imgs .img:active {
  transform: scale(0.95);
}

.comments .spec {
  line-height: 1.4;
  font-size: 24rpx;
  color: #94a3b8;
  padding: 8rpx 16rpx;
  background: rgba(148, 163, 184, 0.08);
  border-radius: 12rpx;
  display: inline-block;
}

/* 评论摘要样式 */
.comment-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 36rpx;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
  transition: all 200ms ease;
  text-decoration: none;
}

.comment-summary:active {
  background: rgba(248, 250, 252, 0.8);
  transform: scale(0.995);
}

.comment-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.comment-title {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.comment-count {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.view-all {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  transition: color 200ms ease;
}

.comment-summary:active .view-all {
  color: #42A5F5;
}

.comment-summary .arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
  transition: all 200ms ease;
}

.comment-summary:active .arrow-icon {
  opacity: 1;
  transform: translateX(2rpx);
}

.goods-attr {
  width: 750rpx;
  height: auto;
  overflow: hidden;
  padding: 0 31.25rpx 25rpx 31.25rpx;
  background: #fff;
}

.goods-attr .t {
  width: 687.5rpx;
  height: 104rpx;
  line-height: 104rpx;
  font-size: 38.5rpx;
}

.goods-attr .item {
  width: 687.5rpx;
  height: auto;
  min-height: 68rpx;
  padding: 11rpx 20rpx;
  margin-bottom: 11rpx;
  background: #f7f7f7;
  font-size: 38.5rpx;
  display: flex;
  align-items: flex-start;
  overflow: hidden;
}

.goods-attr .left {
  width: 18%;
  min-height: 45rpx;
  line-height: 45rpx;
  color: #999;
  flex-shrink: 0;
}

.goods-attr .right {
  float: left;
  font-size: 24rpx;
  margin-left: 20rpx;
  width: 82%;
  min-height: 45rpx;
  height: auto;
  line-height: 45rpx;
  color: #333;
  word-break: break-all;
  white-space: normal;
  overflow: visible;
}

.detail {
  width: 750rpx;
  height: auto;
  overflow: hidden;
}

.detail image {
  width: 750rpx;
  display: block;
}

.common-problem {
  width: 750rpx;
  height: auto;
  overflow: hidden;
}

.common-problem .h {
  position: relative;
  height: 145.5rpx;
  width: 750rpx;
  padding: 56.25rpx 0;
  background: #fff;
  text-align: center;
}

.common-problem .h .line {
  display: inline-block;
  position: absolute;
  top: 72rpx;
  left: 0;
  z-index: 2;
  height: 1px;
  margin-left: 225rpx;
  width: 300rpx;
  background: #ccc;
}

.common-problem .h .title {
  display: inline-block;
  position: absolute;
  top: 56.125rpx;
  left: 0;
  z-index: 3;
  height: 33rpx;
  margin-left: 285rpx;
  width: 180rpx;
  background: #fff;
}

.common-problem .b {
  width: 750rpx;
  height: auto;
  overflow: hidden;
  padding: 0rpx 30rpx;
  background: #fff;
}

.common-problem .item {
  height: auto;
  overflow: hidden;
  padding-bottom: 25rpx;
}

.common-problem .question-box .spot {
  float: left;
  display: block;
  height: 8rpx;
  width: 8rpx;
  background: #42A5F5;
  border-radius: 50%;
  margin-top: 11rpx;
}

.common-problem .question-box .question {
  float: left;
  line-height: 30rpx;
  padding-left: 8rpx;
  display: block;
  font-size: 26rpx;
  padding-bottom: 15rpx;
  color: #303030;
}

.common-problem .answer {
  line-height: 36rpx;
  padding-left: 16rpx;
  font-size: 26rpx;
  color: #787878;
}

.recommend-section {
  margin-top: 40rpx;
  padding: 0 30rpx 40rpx;
  background: #fff;
}

.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  position: relative;
}

.divider-line {
  width: 40rpx;
  height: 3rpx;
  background: #07c160;
  border-radius: 3rpx;
}


.recommend-goods {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  margin-bottom: 45rpx;
}

.recommend-item {
  width: 50%;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.recommend-link {
  display: block;
  border-radius: 12rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.official-tag {
  position: absolute;
  top: 16rpx;
  left: 0;
  background-color: #09AFFF;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 14rpx;
  border-radius: 0 20rpx 20rpx 0;
  z-index: 10;
}

.recommend-img {
  width: 100%;
  height: 340rpx;
  display: block;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.recommend-brief {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.recommend-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommend-price {
  display: flex;
  align-items: baseline;
}

.recommend-price .symbol {
  font-size: 24rpx;
  color: #FF5252;
  margin-right: 2rpx;
}

.recommend-price .price-num {
  font-size: 32rpx;
  color: #FF5252;
  font-weight: 600;
}

.cart-add {
  width: 60rpx;
  height: 60rpx;
  background: #09AFFF;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.cart-add:active {
  transform: scale(0.92);
  background: #429846;
}

.cart-add::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0);
  opacity: 0;
  transition: all 0.3s;
}

.cart-add:active::after {
  transform: scale(1.3);
  opacity: 1;
}

.cart-add image {
  width: 34rpx;
  height: 34rpx;
  filter: brightness(10);
  z-index: 2;
}


/* 底部操作栏 */
.bottom-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 1000;
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: 16rpx 24rpx;
  box-sizing: border-box;
  gap: 20rpx;
}

/* 图标按钮组 */
.icon-group {
  display: flex;
  gap: 16rpx;
}

.icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 88rpx;
  height: 88rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(100, 116, 139, 0.1);
  border-radius: 20rpx;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.icon-btn:active {
  transform: scale(0.95);
  background: rgba(66, 165, 245, 0.1);
  border-color: #42A5F5;
}

.icon-btn .icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
  transition: all 200ms ease;
  margin-bottom: 4rpx;
}

.icon-btn:active .icon {
  opacity: 1;
  transform: scale(1.1);
}

.icon-btn .icon-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  transition: color 200ms ease;
}

.icon-btn:active .icon-text {
  color: #42A5F5;
}

/* 分享按钮特殊样式 */
.share-btn:active {
  background: rgba(34, 197, 94, 0.1);
  border-color: #22c55e;
}

.share-btn:active .icon {
  opacity: 1;
  transform: scale(1.1);
}

.share-btn:active .icon-text {
  color: #22c55e;
}

/* 购物车徽章 */
.cart-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  min-width: 28rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 600;
  line-height: 28rpx;
  text-align: center;
  border-radius: 14rpx;
  padding: 0 6rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.3);
  z-index: 10;
}

/* 操作按钮组 */
.action-group {
  flex: 1;
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  border-radius: 44rpx;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 600ms ease;
}

.action-btn:active::before {
  left: 100%;
}

.action-btn:active {
  transform: scale(0.98);
}

/* 立即购买按钮 */
.buy-now-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.3);
}

.buy-now-btn:active {
  box-shadow: 0 2rpx 12rpx rgba(245, 158, 11, 0.4);
}

/* 加入购物车按钮 */
.add-cart-btn {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.3);
}

.add-cart-btn:active {
  box-shadow: 0 2rpx 12rpx rgba(66, 165, 245, 0.4);
}

/* 响应式优化 */
@media (max-width: 375px) {
  .bottom-btn {
    height: 100rpx;
    padding: 12rpx 20rpx;
    gap: 16rpx;
  }

  .icon-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 16rpx;
  }

  .icon-btn .icon {
    width: 36rpx;
    height: 36rpx;
    margin-bottom: 2rpx;
  }

  .icon-btn .icon-text {
    font-size: 18rpx;
  }

  .cart-badge {
    width: 24rpx;
    height: 24rpx;
    font-size: 16rpx;
    line-height: 24rpx;
    border-radius: 12rpx;
    top: 6rpx;
    right: 6rpx;
  }

  .action-btn {
    height: 72rpx;
    font-size: 28rpx;
    border-radius: 36rpx;
  }

  .action-group {
    gap: 10rpx;
  }

  /* 规格选择器响应式 */
  .spec-selector {
    padding: 28rpx 32rpx;
  }

  .spec-label {
    font-size: 26rpx;
    padding-left: 18rpx;
  }

  .spec-label::before {
    width: 5rpx;
    height: 14rpx;
  }

  .spec-text {
    font-size: 28rpx;
  }

  .spec-arrow {
    width: 44rpx;
    height: 44rpx;
  }

  .arrow-icon {
    width: 22rpx;
    height: 22rpx;
  }

  /* 弹窗中的规格显示 */
  .attr-pop .spec-prefix {
    font-size: 22rpx;
  }

  .attr-pop .spec-value {
    font-size: 24rpx;
  }
}
@import "../../lib/wxParse/wxParse.wxss";

.attr-pop-box {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 200ms ease-out;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.attr-pop {
  width: 100%;
  max-height: calc(100vh - 240rpx);
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 200ms cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.attr-pop .close {
  position: absolute;
  width: 64rpx;
  height: 64rpx;
  right: 24rpx;
  top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 200ms ease;
  z-index: 10;
}

.attr-pop .close:active {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.attr-pop .close .icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 1;
  filter: brightness(0) invert(1);
}

.attr-pop .img-info {
  display: flex;
  align-items: center;
  padding: 40rpx 36rpx 32rpx 36rpx;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.attr-pop .img {
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16rpx;
  margin-right: 24rpx;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  flex-shrink: 0;
}

.attr-pop .img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attr-pop .info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.attr-pop .p {
  font-size: 36rpx;
  color: #ef4444;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.attr-pop .p::before {
  content: '￥';
  font-size: 28rpx;
  margin-right: 4rpx;
  font-weight: 600;
}

.attr-pop .selected-spec {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(66, 165, 245, 0.08);
  border-radius: 12rpx;
  border: 1rpx solid rgba(66, 165, 245, 0.15);
}

.attr-pop .spec-prefix {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  margin-right: 8rpx;
}

.attr-pop .spec-value {
  font-size: 26rpx;
  color: #42A5F5;
  font-weight: 600;
  flex: 1;
}

.spec-con {
  width: 100%;
  padding: 32rpx 36rpx;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  -webkit-overflow-scrolling: touch;
}

.spec-con::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

.spec-con .name {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
}

.spec-con .values {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.spec-con .value {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80rpx;
  height: 72rpx;
  padding: 0 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(100, 116, 139, 0.2);
  border-radius: 36rpx;
  font-size: 28rpx;
  color: #475569;
  font-weight: 500;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
}

.spec-con .value:active {
  transform: scale(0.98);
}

.spec-con .value.disable {
  background: rgba(148, 163, 184, 0.1);
  border-color: rgba(148, 163, 184, 0.2);
  color: #94a3b8;
  opacity: 0.6;
}

.spec-con .value.selected {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  border-color: transparent;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.25);
  transform: translateY(-1rpx);
}

/* 数量选择器样式 */
.number-item {
  margin-bottom: 40rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  background: rgba(248, 250, 252, 0.5);
  border: 2rpx solid transparent;
  transition: all 300ms ease;
}

.number-item.spec-selected {
  background: rgba(66, 165, 245, 0.08);
  border-color: rgba(66, 165, 245, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.15);
}

.number-item .name {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
}

.number-item .selnum {
  width: 240rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(100, 116, 139, 0.2);
  border-radius: 40rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 200ms ease;
}

.number-item.spec-selected .selnum {
  background: #ffffff;
  border-color: rgba(66, 165, 245, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.2);
}

.number-item .cut,
.number-item .add {
  width: 80rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #64748b;
  background: transparent;
  transition: all 200ms ease;
  user-select: none;
}

.number-item .cut:active,
.number-item .add:active {
  background: rgba(66, 165, 245, 0.1);
  color: #42A5F5;
  transform: scale(0.95);
}

.number-item .cut.disabled,
.number-item .add.disabled {
  color: #cbd5e1;
  opacity: 0.5;
}

.number-item .number {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  background: transparent;
  border: none;
  text-align: center;
}

/* 库存和规格提示样式 */
.number-item .stock-hint,
.number-item .spec-hint {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.number-item .stock-text {
  font-size: 24rpx;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(16, 185, 129, 0.2);
}

.number-item .hint-text {
  font-size: 24rpx;
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(245, 158, 11, 0.2);
}

/* 弹窗底部操作区域 */
.attr-pop .bottom-actions {
  padding: 24rpx 36rpx;
  background: #ffffff;
  border-top: 1rpx solid rgba(241, 245, 249, 0.8);
  flex-shrink: 0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.attr-pop .confirm-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.3);
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.attr-pop .confirm-btn.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
  animation: pulse 2s infinite;
}

.attr-pop .confirm-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(66, 165, 245, 0.4);
}

.attr-pop .confirm-btn.active:active {
  box-shadow: 0 2rpx 12rpx rgba(16, 185, 129, 0.4);
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.4);
  }
}

/* 分享弹窗样式 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.share-content {
  width: 100%;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 32rpx 60rpx;
  box-sizing: border-box;
  animation: slideUp 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.share-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 40rpx;
}

.share-options {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(100, 116, 139, 0.1);
  border-radius: 24rpx;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  margin: 0;
}

.share-option:active {
  transform: scale(0.95);
  background: rgba(66, 165, 245, 0.1);
  border-color: #42A5F5;
}

.share-option::after {
  border: none;
}

.share-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.8;
  transition: all 200ms ease;
}

.share-option:active .share-icon {
  opacity: 1;
  transform: scale(1.1);
}

.share-text {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  transition: color 200ms ease;
}

.share-option:active .share-text {
  color: #42A5F5;
}

.share-cancel {
  width: 100%;
  height: 88rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(100, 116, 139, 0.1);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #64748b;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.share-cancel:active {
  transform: scale(0.98);
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #ef4444;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
