var app = getApp();
var WxParse = require('../../lib/wxParse/wxParse.js');
var util = require('../../utils/util.js');
var api = require('../../config/api.js');
var cartService = require('../../services/cart.js');

Page({
  // 格式化商品简介，支持富文本内容
  formatGoodsBrief: function(text) {
    if (!text) return '';

    // 处理富文本内容，确保在小程序中正确显示
    let formattedText = text;

    // 先进行基本的HTML实体解码（如果需要）
    formattedText = formattedText
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // 如果是富文本HTML内容，进行基本的清理和转换
    if (text.includes('<') && text.includes('>')) {
      // 保留基本的HTML标签，移除不支持的标签和属性
      formattedText = formattedText
        // 处理 font 标签的颜色属性，转换为 span 标签
        .replace(/<font\s+color\s*=\s*["']([^"']+)["'][^>]*>(.*?)<\/font>/gi, '<span style="color:$1">$2</span>')
        // 处理没有引号的 color 属性
        .replace(/<font\s+color\s*=\s*([^>\s]+)[^>]*>(.*?)<\/font>/gi, '<span style="color:$1">$2</span>')
        // 处理其他 font 标签属性（移除标签但保留内容）
        .replace(/<font[^>]*>(.*?)<\/font>/gi, '$1')
        // 保留基本格式标签
        .replace(/<(\/?)strong>/g, '<$1b>')
        .replace(/<(\/?)em>/g, '<$1i>')
        .replace(/<(\/?)u>/g, '<$1u>')
        // 处理段落标签
        .replace(/<p[^>]*>/g, '<div>')
        .replace(/<\/p>/g, '</div>')
        // 处理换行
        .replace(/<br[^>]*>/g, '<br/>')
        // 保留 span 标签的样式属性（小程序 rich-text 支持的样式）
        .replace(/<span\s+style=["']([^"']*)["'][^>]*>/gi, function(match, styles) {
          // 只保留小程序支持的样式属性
          const supportedStyles = [];
          if (styles.includes('color:')) {
            const colorMatch = styles.match(/color:\s*([^;]+)/);
            if (colorMatch) supportedStyles.push(`color:${colorMatch[1].trim()}`);
          }
          if (styles.includes('font-weight:')) {
            const fontWeightMatch = styles.match(/font-weight:\s*([^;]+)/);
            if (fontWeightMatch) supportedStyles.push(`font-weight:${fontWeightMatch[1].trim()}`);
          }
          return supportedStyles.length > 0 ? `<span style="${supportedStyles.join(';')}">` : '<span>';
        })
        // 移除其他不支持的标签但保留内容
        .replace(/<\/?(?:div|section|article|p)[^>]*>/g, '');
    }

    // 继续支持 **文本** 标记的加粗红色功能
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<span style="color:#ef4444;font-weight:bold">$1</span>');

    return formattedText;
  },

  data: {
    id: 0,
    goods: {},
    gallery: [],
    attribute: [],
    issueList: [],
    comment: [],
    brand: {},
    specificationList: [],
    productList: [],
    relatedGoods: [],
    cartGoodsCount: 0,
    userHasCollect: 0,
    number: 1,
    checkedSpecText: '请选择规格数量',
    openAttr: false,
    noCollectImage: "/static/images/icon_collect.png",
    hasCollectImage: "/static/images/icon_collect_checked.png",
    collectBackImage: "/static/images/icon_collect.png",
    formattedGoodsBrief: '',   // 格式化后的商品简介
    showDebug: false,          // 是否显示调试信息
    // 规格相关数据
    currentProduct: null,      // 当前选中的产品信息
    currentPrice: 0,           // 当前价格
    currentStock: 0,           // 当前库存
    canAddToCart: false,       // 是否可以添加到购物车
    // 分享相关数据
    showShareModal: false,     // 是否显示分享弹窗
    // 购买类型
    purchaseType: '',          // 购买类型：'addToCart' 或 'instantlyBuy'
    // 会员日提示
    memberTipText: ''
  },

  // 格式化分享图片URL，确保符合微信分享要求
  formatShareImageUrl: function(url) {
    if (!url) {
      return '';
    }

    // 使用项目的formatImageUrl函数
    let formattedUrl = util.formatImageUrl(url);

    // 如果是localhost，需要替换为实际的域名
    if (formattedUrl.includes('localhost:9999')) {
      // 使用配置文件中的域名
      formattedUrl = formattedUrl.replace('http://localhost:9999', 'https://www.sxwjsm.com');
    }

    // 确保使用HTTPS协议（微信分享要求）
    if (formattedUrl.startsWith('http://')) {
      formattedUrl = formattedUrl.replace('http://', 'https://');
    }

    return formattedUrl;
  },
  getGoodsInfo: function () {
    let that = this;
    util.request(api.GoodsDetail, { id: that.data.id }).then(function (res) {
      if (res.success) {
        // 处理详情标签，将逗号分割的字符串转换为数组
        let detailTagArray = [];
        if (res.data.goods.detailTag && typeof res.data.goods.detailTag === 'string') {
          detailTagArray = res.data.goods.detailTag.split(',').filter(tag => tag.trim() !== '');
        }

        that.setData({
          goods: res.data.goods,
          gallery: res.data.goodsGalleryList,
          attribute: res.data.goodsAttributeList,
          issueList: res.data.goodsIssueList,
          comment: res.data.comment,
          brand: res.data.brand,
          specificationList: res.data.goodsSpecificationList,
          productList: res.data.productList,
          userHasCollect: res.data.userHasCollect,
          formattedGoodsBrief: that.formatGoodsBrief(res.data.goods.goodsBrief),
          detailTagArray: detailTagArray, // 添加处理后的标签数组
          // 初始化规格相关数据
          currentPrice: res.data.goods.unitPrice || res.data.goods.retailPrice,
          currentStock: res.data.goods.goodsNumber
        });

        // 如果是单规格商品，直接设置可以添加到购物车
        if (!res.data.goodsSpecificationList || res.data.goodsSpecificationList.length === 0) {
          that.setData({
            canAddToCart: res.data.goods.goodsNumber > 0,
            currentProduct: res.data.productList && res.data.productList.length > 0 ? res.data.productList[0] : null
          });
        }

        if (res.data.userHasCollect == 1) {
          that.setData({
            'collectBackImage': that.data.hasCollectImage
          });
        } else {
          that.setData({
            'collectBackImage': that.data.noCollectImage
          });
        }

        WxParse.wxParse('goodsDetail', 'html', res.data.goods.goodsDesc, that);

        that.getGoodsRelated();
      }
    });

  },
  getGoodsRelated: function () {
    let that = this;
    util.request(api.GoodsRelated, { id: that.data.id }).then(function (res) {
      if (res.success) {
        that.setData({
          relatedGoods: res.data,
        });
      }
    });
  },
  
  // 获取会员日提示信息
  getMemberDayTip: function () {
    let that = this;
    util.request(api.GoodsMemberDayTip, { position: 'goods' }).then(function (res) {
      if (res.success && res.data) {
        that.setData({
          memberTipText: res.data.tipText
        });
      }
    }).catch(function (error) {
      console.log('获取会员日提示失败:', error);
    });
  },
  
  getCartList: function () {
    const that = this;
    if (wx.getStorageSync('userInfo'))
      util.request(api.CartGoodsCount).then(function (res) {
        if (res.success) {
          that.setData({
            cartGoodsCount: res.data
          });
        }
      });
  },
  clickSkuValue: function (event) {
    let that = this;
    let specNameId = event.currentTarget.dataset.nameId;
    let specValueId = event.currentTarget.dataset.valueId;

    //判断是否可以点击

    //TODO 性能优化，可在wx:for中添加index，可以直接获取点击的属性名和属性值，不用循环
    let _specificationList = this.data.specificationList;
    for (let i = 0; i < _specificationList.length; i++) {
      if (_specificationList[i].specificationId == specNameId) {
        for (let j = 0; j < _specificationList[i].valueList.length; j++) {
          if (_specificationList[i].valueList[j].id == specValueId) {
            //如果已经选中，则反选
            if (_specificationList[i].valueList[j].checked) {
              _specificationList[i].valueList[j].checked = false;
            } else {
              _specificationList[i].valueList[j].checked = true;
            }
          } else {
            _specificationList[i].valueList[j].checked = false;
          }
        }
      }
    }
    this.setData({
      'specificationList': _specificationList
    });
    //重新计算spec改变后的信息
    this.changeSpecInfo();

    //重新计算哪些值不可以点击
  },

  //获取选中的规格信息
  getCheckedSpecValue: function () {
    let checkedValues = [];
    let _specificationList = this.data.specificationList;
    for (let i = 0; i < _specificationList.length; i++) {
      let _checkedObj = {
        nameId: _specificationList[i].specification_id,
        valueId: 0,
        valueText: ''
      };
      for (let j = 0; j < _specificationList[i].valueList.length; j++) {
        if (_specificationList[i].valueList[j].checked) {
          _checkedObj.valueId = _specificationList[i].valueList[j].id;
          _checkedObj.valueText = _specificationList[i].valueList[j].value;
        }
      }
      checkedValues.push(_checkedObj);
    }

    return checkedValues;

  },
  //根据已选的值，计算其它值的状态
  setSpecValueStatus: function () {

  },
  //判断规格是否选择完整
  isCheckedAllSpec: function () {
    return !this.getCheckedSpecValue().some(function (v) {
      if (v.valueId == 0) {
        return true;
      }
    });
  },
  getCheckedSpecKey: function () {
    let checkedValue = this.getCheckedSpecValue().map(function (v) {
      return v.valueId;
    });

    return checkedValue.join('_');
  },
  changeSpecInfo: function () {
    let checkedNameValue = this.getCheckedSpecValue();

    //设置选择的信息
    let checkedValue = checkedNameValue.filter(function (v) {
      if (v.valueId != 0) {
        return true;
      } else {
        return false;
      }
    }).map(function (v) {
      return v.valueText;
    });

    if (checkedValue.length > 0) {
      // 格式化规格显示文本
      let specText = checkedValue.join(' · ');

      // 如果规格选择完整，添加数量信息
      if (this.isCheckedAllSpec()) {
        specText += ' × ' + this.data.number + '件';
      }

      this.setData({
        'checkedSpecText': specText
      });

      // 检查是否选择了完整的规格
      if (this.isCheckedAllSpec()) {
        // 根据选择的规格组合查找对应的产品信息
        let specKey = this.getCheckedSpecKey();
        let that = this;

        // 调用后端API验证规格组合
        util.request(api.GoodsValidateSpec, {
          goodsId: that.data.goods.id,
          specIds: specKey
        }).then(function(res) {
          if (res.success && res.data.valid) {
            // 规格组合有效，更新价格和库存信息
            that.setData({
              'currentProduct': { id: res.data.productId },
              'currentPrice': res.data.price,
              'currentStock': res.data.stock,
              'canAddToCart': res.data.canAddToCart
            });
          } else {
            // 规格组合无效
            that.setData({
              'currentProduct': null,
              'currentPrice': that.data.goods.retailPrice,
              'currentStock': 0,
              'canAddToCart': false
            });
          }
        }).catch(function(error) {
          console.error('验证规格组合失败:', error);
          // 降级处理：使用本地数据
          let matchedProducts = that.getCheckedProductItem(specKey);
          if (matchedProducts && matchedProducts.length > 0) {
            let product = matchedProducts[0];
            that.setData({
              'currentProduct': product,
              'currentPrice': product.retailPrice,
              'currentStock': product.goodsNumber,
              'canAddToCart': product.goodsNumber > 0
            });
          } else {
            that.setData({
              'currentProduct': null,
              'currentPrice': that.data.goods.retailPrice,
              'currentStock': 0,
              'canAddToCart': false
            });
          }
        });
      } else {
        // 规格选择不完整，重置为默认状态
        this.setData({
          'currentProduct': null,
          'currentPrice': this.data.goods.retailPrice,
          'currentStock': this.data.goods.goodsNumber,
          'canAddToCart': false
        });
      }
    } else {
      this.setData({
        'checkedSpecText': '请选择规格数量',
        'currentProduct': null,
        'currentPrice': this.data.goods.retailPrice,
        'currentStock': this.data.goods.goodsNumber,
        'canAddToCart': false
      });
    }

  },
  getCheckedProductItem: function (key) {
    return this.data.productList.filter(function (v) {
      if (v.goodsSpecificationIds == key) {
        return true;
      } else {
        return false;
      }
    });
  },
  onLoad: function (options) {
    // 页面初始化 options为页面跳转所带来的参数
    this.setData({
      id: parseInt(options.id),
      showDebug: options.debug === '1' // 通过URL参数启用调试模式
    });
    var that = this;
    this.getGoodsInfo();
    this.getCartList();
    this.getMemberDayTip();
  },
  onReady: function () {
    // 页面渲染完成

  },
  onShow: function () {
    // 页面显示

  },
  onHide: function () {
    // 页面隐藏

  },
  onUnload: function () {
    // 页面关闭

  },
  // 用户点击右上角分享
  onShareAppMessage: function () {
    let that = this;

    // 获取分享图片URL
    let shareImageUrl = '';
    if (that.data.goods.primaryPicUrl) {
      shareImageUrl = that.data.goods.primaryPicUrl;
    } else if (that.data.gallery && that.data.gallery.length > 0) {
      shareImageUrl = that.data.gallery[0].imgUrl;
    } else if (that.data.goods.listPicUrl) {
      shareImageUrl = that.data.goods.listPicUrl;
    }

    // 格式化分享图片URL
    if (shareImageUrl) {
      shareImageUrl = that.formatShareImageUrl(shareImageUrl);
    }

    return {
      title: that.data.goods.name || '精选商品',
      desc: that.data.goods.goodsBrief || '精选优质商品，品质生活选择',
      path: '/pages/goods/goods?id=' + that.data.id,
      imageUrl: shareImageUrl
    }
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    let that = this;

    // 获取分享图片URL
    let shareImageUrl = '';

    // 优先使用商品主图
    if (that.data.goods.primaryPicUrl) {
      shareImageUrl = that.data.goods.primaryPicUrl;
    }
    // 其次使用轮播图第一张
    else if (that.data.gallery && that.data.gallery.length > 0) {
      shareImageUrl = that.data.gallery[0].imgUrl;
    }
    // 最后使用列表图
    else if (that.data.goods.listPicUrl) {
      shareImageUrl = that.data.goods.listPicUrl;
    }

    // 格式化分享图片URL
    if (shareImageUrl) {
      shareImageUrl = that.formatShareImageUrl(shareImageUrl);
    }

    console.log('分享到朋友圈 - 商品名称:', that.data.goods.name);
    console.log('分享到朋友圈 - 图片URL:', shareImageUrl);
    console.log('分享到朋友圈 - 查询参数:', 'id=' + that.data.id);

    return {
      title: that.data.goods.name || '精选商品',
      query: 'id=' + that.data.id,
      imageUrl: shareImageUrl
    }
  },
  switchAttrPop: function () {
    if (this.data.openAttr == false) {
      this.setData({
        openAttr: !this.data.openAttr
      });
    }
  },
  closeAttr: function () {
    // 关闭弹窗前更新主页面的规格显示文本
    this.changeSpecInfo();

    // 如果规格选择完整且有购买类型，执行相应的购买操作
    if (this.data.canAddToCart && this.data.purchaseType) {
      if (this.data.purchaseType === 'addToCart') {
        // 执行加入购物车操作
        this.executeAddToCart(this.data.goods.id);
      } else if (this.data.purchaseType === 'instantlyBuy') {
        // 执行立即购买操作
        this.executeInstantlyBuy(this.data.goods.id);
      }
      
      // 清除购买类型标记
      this.setData({
        purchaseType: ''
      });
    }

    this.setData({
      openAttr: false,
    });
  },
  addCannelCollect: function () {
    let that = this;
    //添加或是取消收藏
    util.request(api.CollectAddOrDelete, { typeId: 0, valueId: this.data.id }, "POST")
      .then(function (res) {
        let _res = res;
        if (_res.success) {
          if (_res.data.userHasCollect == true) {
            that.setData({
              'collectBackImage': that.data.hasCollectImage
            });
          } else {
            that.setData({
              'collectBackImage': that.data.noCollectImage
            });
          }

        } else {
          wx.showToast({
            image: '/static/images/icon_error.png',
            title: _res.msg,
            mask: true
          });
        }
      });
  },
  openCartPage: function () {
    wx.switchTab({
      url: '/pages/cart/cart',
    });
  },
  addToCart: function (e) {
    const goodsId = e.currentTarget.dataset.valueId;
    let that = this;

    // 检查是否需要选择规格
    if (that.data.specificationList && that.data.specificationList.length > 0) {
      if (!that.data.canAddToCart) {
        // 设置购买类型为加入购物车，然后打开规格选择弹窗
        that.setData({
          openAttr: true,
          purchaseType: 'addToCart' // 标记购买类型
        });
        wx.showToast({
          title: '请选择商品规格',
          icon: 'none',
          duration: 1500
        });
        return;
      }
    }

    // 执行加入购物车操作
    that.executeAddToCart(goodsId);
  },

  // 执行加入购物车操作
  executeAddToCart: function(goodsId) {
    let that = this;
    
    // 获取当前选中的产品ID
    let productId = 0;
    if (that.data.currentProduct) {
      productId = that.data.currentProduct.id;
    } else if (that.data.productList && that.data.productList.length > 0) {
      productId = that.data.productList[0].id;
    }

    cartService.addToCart({
      goodsId: goodsId,
      number: that.data.number || 1,
      productId: productId,
      directAdd: true,  // 直接添加，不再检查规格
      success: function (res) {
        // 更新购物车图标数量
        that.getCartList();
      }
    });
  },
  instantlyBuy(e) {
    if (!wx.getStorageSync('userInfo')) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    const goodsId = e.currentTarget.dataset.valueId;
    let that = this;

    // 检查是否需要选择规格
    if (that.data.specificationList && that.data.specificationList.length > 0) {
      if (!that.data.canAddToCart) {
        // 设置购买类型为立即购买，然后打开规格选择弹窗
        that.setData({
          openAttr: true,
          purchaseType: 'instantlyBuy' // 标记购买类型
        });
        wx.showToast({
          title: '请选择商品规格',
          icon: 'none',
          duration: 1500
        });
        return;
      }
    }

    // 执行立即购买操作
    that.executeInstantlyBuy(goodsId);
  },

  // 执行立即购买操作
  executeInstantlyBuy: function(goodsId) {
    let that = this;
    
    // 获取当前选中的产品ID
    let productId = 0;
    if (that.data.currentProduct) {
      productId = that.data.currentProduct.id;
    } else if (that.data.productList && that.data.productList.length > 0) {
      productId = that.data.productList[0].id;
    }

    let url = '/pages/shopping/checkout/checkout';
    url += '?type=1&goodsId=' + goodsId;
    if (productId > 0) {
      url += '&productId=' + productId;
    }
    // 传递选择的数量和当前价格
    url += '&number=' + that.data.number;
    url += '&currentPrice=' + (that.data.currentPrice || that.data.goods.retailPrice);

    wx.navigateTo({
      url: url
    });
  },
  getFlyAnimation: function (e, goodsId) {
    let that = this;
    const product = this.data.recommendGoods.find(item => item.id === goodsId);
    if (!product) return;

    // 获取商品图片
    const flyImg = product.listPicUrl || '../../static/images/tip/1.jpg';

    // 获取点击位置
    let touch = e.touches[0] || e.changedTouches[0];
    let flyTop = touch.clientY - 25; // 减去一半的飞行元素高度
    let flyLeft = touch.clientX - 25; // 减去一半的飞行元素宽度

    // 设置动画参数
    that.setData({
      openFly: true,
      flyImg: flyImg,
      flyTop: flyTop,
      flyLeft: flyLeft
    });

    // 动画结束后关闭
    setTimeout(function () {
      that.setData({
        openFly: false
      });
    }, 800); // 800ms与动画时长匹配
  },
  cutNumber: function () {
    this.setData({
      number: (this.data.number - 1 > 1) ? this.data.number - 1 : 1
    });
    // 更新规格显示文本中的数量
    this.changeSpecInfo();
  },
  addNumber: function () {
    let maxNumber = 99; // 默认最大数量

    // 如果已选择完整规格且有库存信息，使用库存作为限制
    if (this.data.canAddToCart && this.data.currentStock > 0) {
      maxNumber = this.data.currentStock;
    }

    if (this.data.number < maxNumber) {
      this.setData({
        number: this.data.number + 1
      });
      // 更新规格显示文本中的数量
      this.changeSpecInfo();
    } else {
      // 显示库存不足提示
      let message = this.data.canAddToCart ? '库存不足' : '数量已达上限';
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 显示分享选项弹窗
  showShareOptions: function () {
    this.setData({
      showShareModal: true
    });
  },

  // 隐藏分享选项弹窗
  hideShareOptions: function () {
    this.setData({
      showShareModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 空函数，用于阻止事件冒泡
  },

  // 分享到朋友圈
  shareToTimeline: function () {
    // 隐藏弹窗
    this.hideShareOptions();

    // 提示用户通过右上角菜单分享到朋友圈
    wx.showModal({
      title: '分享到朋友圈',
      content: '请点击右上角的"..."按钮，选择"分享到朋友圈"',
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#42A5F5'
    });
  }
})
