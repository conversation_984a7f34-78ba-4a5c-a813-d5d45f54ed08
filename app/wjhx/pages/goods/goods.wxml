<wxs src="../../utils/format.wxs" module="format" />
<view class="container">
  <swiper class="goodsimgs" indicator-dots="true" autoplay="true" interval="3000" duration="1000">
    <swiper-item wx:for="{{gallery}}" wx:key="{{item.id}}">
      <image src="{{format.formatImageUrl(item.imgUrl)}}" mode="aspectFit"></image>
    </swiper-item>
  </swiper>
  <!-- 会员日小提示 -->
  <view class="member-day-tip" wx:if="{{memberTipText}}">
    <view class="tip-content">
      <text class="tip-text">{{memberTipText}}</text>
    </view>
  </view>
  <view class="service-policy">
    <view class="item" wx:for="{{detailTagArray}}" wx:key="index">{{item}}</view>
  </view>
  <view class="goods-info">
    <view class="c">
      <text class="name">{{goods.name}}</text>
      <rich-text class="desc" nodes="{{formattedGoodsBrief}}" space="nbsp"></rich-text>
      <text class="price">{{currentPrice || goods.unitPrice}}</text>
      <text class="stock" wx:if="{{goods.sellVolume !== null}}">销量：{{goods.sellVolume}}</text>
      <view class="brand" wx:if="{{brand.name}}">
        <navigator url="../brandDetail/brandDetail?id={{brand.brandId}}">
          <text>{{brand.name}}</text>
        </navigator>
      </view>
    </view>
  </view>
  <view class="spec-selector" bindtap="switchAttrPop">
    <view class="spec-content">
      <view class="spec-label">规格选择</view>
      <view class="spec-text {{checkedSpecText === '请选择规格数量' ? 'placeholder' : 'selected'}}">
        {{checkedSpecText}}
      </view>
    </view>
    <view class="spec-arrow">
      <image class="arrow-icon" src="../../static/images/address_right.png"></image>
    </view>
  </view>
  <view class="comments" wx:if="{{comment.count > 0}}">
    <navigator url="../comment/comment?valueId={{goods.id}}&typeId=0" class="comment-summary">
      <view class="comment-info">
        <text class="comment-title">商品评价</text>
        <text class="comment-count">({{comment.count > 999 ? '999+' : comment.count}}条)</text>
      </view>
      <view class="comment-action">
        <text class="view-all">查看全部</text>
        <image class="arrow-icon" src="../../static/images/address_right.png"></image>
      </view>
    </navigator>
  </view>
  <view class="goods-attr">
    <view class="t">商品参数</view>
    <view class="l">
      <view class="item" wx:for="{{attribute}}" wx:key="{{item.name}}">
        <text class="left">{{item.name}}</text>
        <text class="right">{{item.value}}</text>
      </view>
    </view>
  </view>
  <view class="detail">
    <import src="../../lib/wxParse/wxParse.wxml" />
    <template is="wxParse" data="{{wxParseData:goodsDetail.nodes}}" />
  </view>
  <view class="common-problem">
    <view class="h">
      <view class="line"></view>
      <text class="title">常见问题</text>
    </view>
    <view class="b">
      <view class="item" wx:for="{{issueList}}" wx:key="{{item.id}}">
        <view class="question-box">
          <text class="spot"></text>
          <text class="question">{{item.question}}</text>
        </view>
        <view class="answer">{{item.answer}}</view>
      </view>
    </view>
  </view>
  <!-- 大家都在看 -->
  <view class="recommend-section" wx:if="{{relatedGoods.length > 0}}">
    <view class="section-header">
      <view class="section-title">大家都在看</view>
      <view class="divider-line"></view>
    </view>
    <view class="recommend-goods">
      <view class="recommend-item" wx:for="{{relatedGoods}}" wx:key="id">
        <navigator url="/pages/goods/goods?id={{item.id}}" class="recommend-link">
          <view class="official-tag">产地直发</view>
          <image class="recommend-img" src="{{format.formatImageUrl(item.listPicUrl)}}" mode="aspectFill"></image>
          <view class="recommend-info">
            <view class="recommend-name">{{item.name}}</view>
            <view class="recommend-brief">{{item.brief || '精选商品 品质保障'}}</view>
            <view class="recommend-price-info">
              <view class="recommend-price">
                <text class="symbol">¥</text>
                <text class="price-num">{{item.retailPrice}}</text>
              </view>
              <view class="cart-add" catchtap="addToCart" data-value-id="{{item.id}}">
                <image src="/static/images/ic_menu_shoping_nor.png"></image>
              </view>
            </view>
          </view>
        </navigator>
      </view>
    </view>
    <view class="fly-item" hidden="{{!openFly}}" style="top:{{flyTop}}px;left:{{flyLeft}}px;width:{{flyWidth}}px;height:{{flyHeight}}px;animation-duration:{{flyTime}}s">
      <image class="fly-img" src="{{flyImg}}"></image>
    </view>
  </view>
</view>
<view class="attr-pop-box" hidden="{{!openAttr}}">
  <view class="attr-pop">
    <view class="close" bindtap="closeAttr">
      <image class="icon" src="/static/images/icon_close.png"></image>
    </view>
    <view class="img-info">
      <image class="img" src="{{format.formatImageUrl(goods.primaryPicUrl)}}"></image>
      <view class="info">
        <view class="c">
          <view class="p">{{currentPrice || goods.retailPrice}}</view>
          <view class="stock" wx:if="{{currentStock !== null}}">销量：{{goods.sellVolume}}</view>
          <view class="selected-spec" wx:if="{{productList.length>0 && checkedSpecText !== '请选择规格数量'}}">
            <text class="spec-prefix">已选择：</text>
            <text class="spec-value">{{checkedSpecText}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="spec-con">
      <view class="spec-item" wx:for="{{specificationList}}" wx:key="{{item.specificationId}}">
        <view class="name">{{item.name}}</view>
        <view class="values">
          <view class="value {{vitem.checked ? 'selected' : ''}}" bindtap="clickSkuValue" wx:for="{{item.valueList}}" wx:for-item="vitem" wx:key="{{vitem.id}}" data-value-id="{{vitem.id}}" data-name-id="{{vitem.specificationId}}">
            {{vitem.value}}
          </view>
        </view>
      </view>
      <view class="number-item {{canAddToCart ? 'spec-selected' : ''}}">
        <view class="name">数量</view>
        <view class="selnum">
          <view class="cut {{number <= 1 ? 'disabled' : ''}}" bindtap="cutNumber">-</view>
          <input value="{{number}}" class="number" disabled="true" type="number" />
          <view class="add {{(canAddToCart && currentStock > 0 && number >= currentStock) || (!canAddToCart && number >= 99) ? 'disabled' : ''}}" bindtap="addNumber">
            +
          </view>
        </view>
        <view class="stock-hint" wx:if="{{canAddToCart && currentStock > 0}}">
          <!-- <text class="stock-text">销量：{{number}}件</text> -->
        </view>
        <view class="spec-hint" wx:if="{{!canAddToCart && specificationList.length > 0}}">
          <text class="hint-text">请先选择完整规格</text>
        </view>
      </view>
    </view>
    <view class="bottom-actions">
      <button class="confirm-btn {{canAddToCart ? 'active' : ''}}" bindtap="closeAttr">
        <block wx:if="{{purchaseType === 'addToCart'}}">
          {{canAddToCart ? '加入购物车 (' + number + '件)' : '请选择完整规格'}}
        </block>
        <block wx:elif="{{purchaseType === 'instantlyBuy'}}">
          {{canAddToCart ? '立即购买 (' + number + '件)' : '请选择完整规格'}}
        </block>
        <block wx:else>{{canAddToCart ? '确认选择 (' + number + '件)' : '确认选择'}}</block>
      </button>
    </view>
  </view>
</view>
<view class="bottom-btn">
  <view class="icon-group">
    <view class="icon-btn collect-btn" bindtap="addCannelCollect">
      <image class="icon" src="{{ collectBackImage }}"></image>
      <text class="icon-text">收藏</text>
    </view>
    <view class="icon-btn cart-btn" bindtap="openCartPage">
      <view class="cart-badge" wx:if="{{cartGoodsCount > 0}}">{{cartGoodsCount}}</view>
      <image class="icon" src="/static/images/ic_menu_shoping_nor.png"></image>
      <text class="icon-text">购物车</text>
    </view>
  </view>
  <view class="action-group">
    <view class="action-btn buy-now-btn" data-value-id="{{goods.id}}" bindtap="instantlyBuy">
      立即购买
    </view>
    <view class="action-btn add-cart-btn" data-value-id="{{goods.id}}" bindtap="addToCart">
      加入购物车
    </view>
  </view>
</view>