// 管理员订单列表页面
const util = require('../../../../utils/util.js');
const api = require('../../../../config/api.js');

Page({
  data: {
    orderList: [],
    currentStatus: '', // 当前筛选状态
    searchKeyword: '', // 搜索关键词
    sortOrder: 'desc', // 排序方式：desc-倒序(最新优先), asc-正序(最早优先)
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    loadingMore: false
  },

  onLoad: function (options) {
    // 检查管理员权限
    this.checkAdminPermission();
    
    // 获取传入的状态参数
    if (options.status) {
      this.setData({
        currentStatus: options.status
      });
    }
    
    this.loadOrderList();
  },

  /**
   * 检查管理员权限
   */
  checkAdminPermission: function () {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      if (!userInfo || !token || !userInfo.userLevelId || userInfo.userLevelId != 1) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问管理员功能的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return false;
      }
      return true;
    } catch (e) {
      console.error('检查管理员权限失败:', e);
      wx.navigateBack();
      return false;
    }
  },

  /**
   * 加载订单列表
   */
  loadOrderList: function (isRefresh = false) {
    if (!this.checkAdminPermission()) {
      return;
    }

    if (isRefresh) {
      this.setData({
        page: 1,
        hasMore: true,
        orderList: []
      });
    }

    if (this.data.loading || this.data.loadingMore) {
      return;
    }

    this.setData({
      loading: isRefresh || this.data.page === 1,
      loadingMore: !isRefresh && this.data.page > 1
    });

    const params = {
      pageNum: this.data.page,
      pageSize: this.data.pageSize,
      orderStatus: this.data.currentStatus,
      keyword: this.data.searchKeyword,
      // 添加排序参数
      sortField: 'createTime', // 排序字段
      sortOrder: this.data.sortOrder // 排序方式：desc-倒序, asc-正序
    };

    // 调试信息：记录请求参数
    console.log('订单列表请求参数:', params);

    // 使用管理员订单列表接口
    util.request(api.AdminOrderList, params).then(res => {
      this.handleOrderListResponse(res, isRefresh);
    }).catch(err => {
      console.error('获取订单列表失败:', err);
      this.setData({
        loading: false,
        loadingMore: false
      });

      // 显示错误提示
      wx.showToast({
        title: '获取订单列表失败',
        icon: 'none',
        duration: 2000
      });

      // 如果是首次加载失败，可以尝试使用模拟数据或显示空状态
      if (isRefresh || this.data.page === 1) {
        this.setData({
          orderList: [],
          hasMore: false
        });
      }
    });
  },

  /**
   * 处理订单列表响应
   */
  handleOrderListResponse: function (res, isRefresh) {
    this.setData({
      loading: false,
      loadingMore: false
    });

    if (res.success) {
      const newList = res.data.list || [];

      // 格式化订单数据，特别是日期格式
      const formattedList = newList.map(order => {
        return {
          ...order,
          createTime: this.formatOrderTime(order.createTime),
          createTimeDisplay: this.formatOrderTimeDisplay(order.createTime),
          timeAgo: this.getTimeAgo(order.createTime),
          // 保存原始时间用于前端显示和备用
          originalCreateTime: order.createTime
        };
      });

      // 数据已经从后端排序好了，直接使用
      let orderList;
      if (isRefresh) {
        orderList = formattedList;
      } else {
        // 分页加载时直接追加，因为后端已经按照正确的排序返回数据
        orderList = [...this.data.orderList, ...formattedList];
      }

      this.setData({
        orderList: orderList,
        hasMore: res.data.pageNum < res.data.pages,
        page: this.data.page + 1
      });
    } else {
      wx.showToast({
        title: res.msg || '获取订单列表失败',
        icon: 'none'
      });
    }
  },

  /**
   * 按创建日期排序订单列表（前端排序 - 备用方法）
   * 注意：现在主要使用后端排序，这个方法作为备用或特殊情况使用
   */
  sortOrdersByDate: function(orderList) {
    return orderList.sort((a, b) => {
      try {
        // 使用原始时间进行排序
        const timeA = new Date(a.originalCreateTime || a.createTime);
        const timeB = new Date(b.originalCreateTime || b.createTime);

        // 根据排序方式进行排序
        if (this.data.sortOrder === 'desc') {
          // 倒序排序：最新的在前面
          return timeB.getTime() - timeA.getTime();
        } else {
          // 正序排序：最早的在前面
          return timeA.getTime() - timeB.getTime();
        }
      } catch (e) {
        console.error('排序时间比较失败:', e);
        return 0;
      }
    });
  },

  /**
   * 格式化订单时间 - 简洁格式
   */
  formatOrderTime: function(dateStr) {
    if (!dateStr) return '';

    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;

      const now = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');

      // 如果是今年，只显示月-日 时:分
      if (year === now.getFullYear()) {
        return `${month}-${day} ${hour}:${minute}`;
      } else {
        // 如果是其他年份，显示年/月/日 时:分
        return `${year}/${month}/${day} ${hour}:${minute}`;
      }
    } catch (e) {
      console.error('日期格式化失败:', e);
      return dateStr;
    }
  },

  /**
   * 格式化订单时间显示 - 详细格式
   */
  formatOrderTimeDisplay: function(dateStr) {
    if (!dateStr) return '';

    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      console.error('日期格式化失败:', e);
      return dateStr;
    }
  },

  /**
   * 获取相对时间（多久前）
   */
  getTimeAgo: function(dateStr) {
    if (!dateStr) return '';

    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';

      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMinutes < 1) {
        return '刚刚';
      } else if (diffMinutes < 60) {
        return `${diffMinutes}分钟前`;
      } else if (diffHours < 24) {
        return `${diffHours}小时前`;
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return this.formatOrderTime(dateStr);
      }
    } catch (e) {
      console.error('相对时间计算失败:', e);
      return '';
    }
  },

  /**
   * 显示时间详情
   */
  showTimeDetail: function(e) {
    const { time, order } = e.currentTarget.dataset;
    wx.showModal({
      title: `订单 ${order}`,
      content: `创建时间：${time}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索订单
   */
  searchOrders: function () {
    this.loadOrderList(true);
  },

  /**
   * 按状态筛选
   */
  filterByStatus: function (e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentStatus: status
    });
    this.loadOrderList(true);
  },

  /**
   * 改变排序方式
   */
  changeSortOrder: function(e) {
    const order = e.currentTarget.dataset.order;
    if (order === this.data.sortOrder) {
      return; // 如果点击的是当前排序方式，不做任何操作
    }

    this.setData({
      sortOrder: order
    });

    // 重新从接口获取排序后的数据
    this.loadOrderList(true);

    wx.showToast({
      title: order === 'desc' ? '按最新优先排序' : '按最早优先排序',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function (e) {
    // 阻止事件冒泡（小程序兼容性处理）
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    const orderId = e.currentTarget.dataset.id;

    // 跳转到订单详情页面
    wx.navigateTo({
      url: `/pages/ucenter/orderDetail/orderDetail?id=${orderId}`
    });
  },



  /**
   * 发货操作
   */
  deliverOrder: function (e) {
    // 阻止事件冒泡（小程序兼容性处理）
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认发货',
      content: '确定要将此订单标记为已发货吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });

          util.request(api.AdminOrderDeliver, { orderId: orderId }).then(response => {
            wx.hideLoading();
            if (response.success) {
              wx.showToast({
                title: '发货成功',
                icon: 'success'
              });
              this.loadOrderList(true);
            } else {
              wx.showToast({
                title: response.msg || '发货失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.hideLoading();
            wx.showToast({
              title: '发货失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  /**
   * 取消订单
   */
  cancelOrder: function (e) {
    // 阻止事件冒泡（小程序兼容性处理）
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '取消订单',
      content: '确定要取消此订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });

          util.request(api.AdminOrderCancel, { orderId: orderId }).then(response => {
            wx.hideLoading();
            if (response.success) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              });
              this.loadOrderList(true);
            } else {
              wx.showToast({
                title: response.msg || '取消失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.hideLoading();
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadOrderList(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadOrderList();
    }
  },

  /**
   * 返回到我的页面
   */
  goBackToMe: function () {
    const pages = getCurrentPages();

    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];

      // 检查上一页是否是"我的"页面或管理员中心页面
      if (prevPage.route === 'pages/ucenter/me/me' ||
          prevPage.route === 'pages/ucenter/admin/admin') {
        wx.navigateBack();
        return;
      }
    }

    // 如果不是从"我的"页面或管理员中心跳转过来的，直接跳转到"我的"页面
    wx.switchTab({
      url: '/pages/ucenter/me/me'
    });
  }
});
