// 管理员用户列表页面
const util = require('../../../../utils/util.js');
const api = require('../../../../config/api.js');

Page({
  data: {
    userList: [],
    currentType: '', // 当前筛选类型
    searchKeyword: '', // 搜索关键词
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    loadingMore: false,
    showUserDetail: false,
    userDetail: {},
    userStats: {
      totalUsers: 0,
      newUsers: 0,
      activeUsers: 0,
      vipUsers: 0,
      promoterUsers: 0
    }
  },

  onLoad: function (options) {
    // 检查管理员权限
    this.checkAdminPermission();
    
    // 获取传入的类型参数
    if (options.type) {
      this.setData({
        currentType: options.type
      });
    }
    
    this.loadUserStats();
    this.loadUserList();
  },

  /**
   * 检查管理员权限
   */
  checkAdminPermission: function () {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      if (!userInfo || !token || !userInfo.userLevelId || userInfo.userLevelId != 1) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问管理员功能的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return false;
      }
      return true;
    } catch (e) {
      console.error('检查管理员权限失败:', e);
      wx.navigateBack();
      return false;
    }
  },

  /**
   * 加载用户统计数据
   */
  loadUserStats: function () {
    // 使用真实接口获取统计数据
    util.request(api.AdminStats).then(res => {
      if (res.success && res.data) {
        this.setData({
          userStats: {
            totalUsers: res.data.totalUsers || 0,
            newUsers: res.data.newUsers || 0,
            activeUsers: res.data.activeUsers || 0,
            vipUsers: res.data.vipUsers || 0,
            promoterUsers: res.data.promoterUsers || 0
          }
        });
      }
    }).catch(err => {
      console.error('获取统计数据失败:', err);
      // 设置默认值
      this.setData({
        userStats: {
          totalUsers: 0,
          newUsers: 0,
          activeUsers: 0,
          vipUsers: 0,
          promoterUsers: 0
        }
      });
    });
  },

  /**
   * 加载用户列表
   */
  loadUserList: function (isRefresh = false) {
    if (!this.checkAdminPermission()) {
      return;
    }

    if (isRefresh) {
      this.setData({
        page: 1,
        hasMore: true,
        userList: []
      });
    }

    if (this.data.loading || this.data.loadingMore) {
      return;
    }

    this.setData({
      loading: isRefresh || this.data.page === 1,
      loadingMore: !isRefresh && this.data.page > 1
    });

    const params = {
      pageNum: this.data.page,
      pageSize: this.data.pageSize,
      type: this.data.currentType,
      keyword: this.data.searchKeyword
    };

    // 使用管理员用户列表接口
    util.request(api.AdminUserList, params).then(res => {
      this.handleUserListResponse(res, isRefresh);
    }).catch(err => {
      console.log('获取用户列表失败，使用模拟数据:', err);
    });
  },

  /**
   * 处理用户列表响应
   */
  handleUserListResponse: function (res, isRefresh) {
    this.setData({
      loading: false,
      loadingMore: false
    });

    if (res.success) {
      const newList = res.data.list || [];
      const userList = isRefresh ? newList : [...this.data.userList, ...newList];

      this.setData({
        userList: userList,
        hasMore: res.data.pageNum < res.data.pages,
        page: this.data.page + 1
      });
    } else {
      wx.showToast({
        title: res.msg || '获取用户列表失败',
        icon: 'none'
      });
    }
  },


  /**
   * 搜索输入
   */
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索用户
   */
  searchUsers: function () {
    this.loadUserList(true);
  },

  /**
   * 按类型筛选
   */
  filterByType: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentType: type
    });
    this.loadUserList(true);
  },

  /**
   * 查看用户详情
   */
  viewUserDetail: function (e) {
    const userId = e.currentTarget.dataset.id;
    const user = this.data.userList.find(item => item.id == userId);
    
    if (user) {
      this.setData({
        userDetail: user,
        showUserDetail: true
      });
    }
  },

  /**
   * 关闭用户详情
   */
  closeUserDetail: function () {
    this.setData({
      showUserDetail: false
    });
  },

  /**
   * 查看用户订单
   */
  viewUserOrders: function (e) {
    const userId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ucenter/admin/orders/orders?userId=${userId}`
    });
  },

  /**
   * 查看推广详情
   */
  viewPromotionDetail: function (e) {
    const userId = e.currentTarget.dataset.userId;
    const userName = e.currentTarget.dataset.userName;
    
    if (userId) {
      wx.navigateTo({
        url: `/pages/ucenter/promotion-user-detail/promotion-user-detail?userId=${userId}&nickname=${encodeURIComponent(userName || '用户详情')}&fromAdmin=true`
      });
    }
  },

  /**
   * 编辑用户
   */
  editUser: function (e) {
    const userId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '编辑用户',
      content: '此功能正在开发中...',
      showCancel: false
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadUserStats();
    this.loadUserList(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadUserList();
    }
  },

  /**
   * 返回到我的页面
   */
  goBackToMe: function () {
    wx.navigateTo({
      url: '/pages/ucenter/me/me'
    });
  }
});
