/* 星星重叠效果测试样式 */
.test-page {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  display: block;
}

.test-item {
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.test-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

/* 星星样式 - 与主页面保持一致 */
.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star-container {
  position: relative;
  display: inline-block;
}

.star {
  font-size: 32rpx;
  line-height: 1;
  display: block;
}

.star-bg {
  color: #ddd;
  position: relative;
  z-index: 1;
}

.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  color: #ffb400;
  z-index: 2;
}

.star-fill.active {
  color: #ffb400;
}
