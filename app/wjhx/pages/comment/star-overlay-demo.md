# 星星重叠显示效果实现

## 设计原理

通过CSS定位让点亮的星星和置灰的星星完全重叠，点亮的星星通过`width`属性控制显示宽度，实现精确的半星效果。

## 结构设计

```xml
<view class="star-container">
  <!-- 背景星星（置灰） -->
  <text class="star star-bg">★</text>
  
  <!-- 前景星星（点亮，通过width控制显示宽度） -->
  <text class="star star-fill active" style="width: 60%">★</text>
</view>
```

## CSS实现

```css
.star-container {
  position: relative;
  display: inline-block;
}

.star {
  font-size: 24rpx;
  line-height: 1;
  display: block;
}

/* 背景星星（置灰） */
.star-bg {
  color: #ddd;
  position: relative;
  z-index: 1;
}

/* 前景星星（点亮） */
.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;  /* 关键：隐藏超出width的部分 */
  color: #ffb400;
  z-index: 2;        /* 确保在背景星星之上 */
}

.star-fill.active {
  color: #ffb400;
}
```

## 显示效果

### 不同评分的显示效果：

1. **评分 1.0**: ★☆☆☆☆
   - 第1颗星：width: 100%（完全点亮）
   - 第2-5颗星：width: 0%（完全置灰）

2. **评分 2.3**: ★★⭐☆☆
   - 第1颗星：width: 100%（完全点亮）
   - 第2颗星：width: 100%（完全点亮）
   - 第3颗星：width: 30%（30%点亮，70%置灰）
   - 第4-5颗星：width: 0%（完全置灰）

3. **评分 4.7**: ★★★★⭐
   - 第1-4颗星：width: 100%（完全点亮）
   - 第5颗星：width: 70%（70%点亮，30%置灰）

## 技术要点

### 1. 绝对定位重叠
- 背景星星作为参考位置
- 前景星星绝对定位到相同位置
- 通过z-index确保层级关系

### 2. overflow: hidden
- 关键属性，隐藏超出width设定宽度的星星部分
- 实现精确的部分显示效果

### 3. 动态width控制
- 通过JavaScript计算每颗星星应该显示的宽度百分比
- 0%：完全置灰
- 1-99%：部分点亮
- 100%：完全点亮

## 数据计算逻辑

```javascript
calculateStarData: function(rating) {
    const numRating = this.normalizeRating(rating);
    const stars = [];
    
    for (let i = 0; i < 5; i++) {
        const starIndex = i + 1;
        let width = 0;
        
        if (starIndex <= Math.floor(numRating)) {
            width = 100; // 完整星星
        } else if (starIndex === Math.ceil(numRating) && numRating % 1 !== 0) {
            width = (numRating % 1) * 100; // 部分星星
        } else {
            width = 0; // 空星
        }
        
        stars.push({
            index: i,
            width: width,
            active: width > 0
        });
    }
    
    return stars;
}
```

## 优势

1. **精确显示**：支持任意小数评分的精确显示
2. **视觉连贯**：星星形状完整，只是颜色渐变
3. **性能优化**：预计算数据，避免WXML中的复杂计算
4. **兼容性好**：使用标准CSS属性，兼容性强
