<!-- 评论页面 -->
<view class="comment-page">

  <!-- 评价统计 -->
  <view class="comment-stats">
    <view class="stats-header">
      <view class="total-rating">
        <text class="rating-score">{{averageRating || '5.0'}}</text>
        <view class="rating-stars">
          <view class="star-container" wx:for="{{averageStarData}}" wx:key="index">
            <text class="star star-bg">★</text>
            <text class="star star-fill {{item.active ? 'active' : ''}}"
                  style="width: {{item.width}}%">★</text>
          </view>
        </view>
      </view>
      <view class="stats-info">
        <text class="total-count">共{{allCount}}条评价</text>
        <text class="satisfaction">好评率{{satisfactionRate || '100'}}%</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <view class="tab-list">
      <view class="tab-item {{showType == 0 ? 'active' : ''}}" bindtap="switchTab" data-type="0">
        <text class="tab-text">全部({{allCount}})</text>
      </view>
      <view class="tab-item {{showType == 1 ? 'active' : ''}}" bindtap="switchTab" data-type="1">
        <text class="tab-text">有图({{hasPicCount}})</text>
      </view>
    </view>
  </view>

  <!-- 评论列表 -->
  <view class="comment-list">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && comments.length === 0}}">
      <view class="loading-animation">
        <view class="loading-dot dot1"></view>
        <view class="loading-dot dot2"></view>
        <view class="loading-dot dot3"></view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 评论项 -->
    <view class="comment-item" wx:for="{{comments}}" wx:key="{{item.id}}">
      <view class="comment-header">
        <view class="user-info">
          <image class="user-avatar" src="{{item.userInfo.avatar}}" mode="aspectFill"></image>
          <view class="user-detail">
            <text class="user-name">{{item.userInfo.nickname}}</text>
            <view class="rating-container">
              <view class="rating-stars">
                <view class="star-container" wx:for="{{item.starData}}" wx:key="index">
                  <text class="star star-bg">★</text>
                  <text class="star star-fill {{item.active ? 'active' : ''}}"
                        style="width: {{item.width}}%">★</text>
                </view>
              </view>
              <text class="comment-time">{{item.createTime}}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="comment-content">
        <text class="comment-text">{{item.content}}</text>
      </view>

      <!-- 评价图片 -->
      <view class="comment-images" wx:if="{{item.picList && item.picList.length > 0}}">
        <view class="image-grid">
          <image class="comment-image"
                 wx:for="{{item.picList}}"
                 wx:for-item="pic"
                 wx:key="index"
                 src="{{pic}}"
                 mode="aspectFill"
                 bindtap="previewImage"
                 data-current="{{pic}}"
                 data-urls="{{item.picList}}"></image>
        </view>
      </view>

      <!-- 商品规格信息 -->
      <view class="product-spec" wx:if="{{item.specifications}}">
        <text class="spec-text">规格：{{item.specifications}}</text>
      </view>

      <!-- 商家回复 -->
      <view class="merchant-reply" wx:if="{{item.replyContent}}">
        <view class="reply-header">
          <text class="reply-label">商家回复：</text>
        </view>
        <text class="reply-content">{{item.replyContent}}</text>
      </view>

      <!-- 操作按钮 -->
      <!--<view class="comment-actions">
        <view class="action-btn {{item.isLiked ? 'liked' : ''}}" bindtap="toggleLike" data-id="{{item.id}}">
          <text class="action-icon">👍</text>
          <text class="action-text">{{item.likeCount || 0}}</text>
        </view>
        <view class="action-btn" bindtap="showReplyInput" data-id="{{item.id}}">
          <text class="action-icon">💬</text>
          <text class="action-text">回复</text>
        </view>
      </view>-->
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:if="{{!loading && comments.length === 0}}">
      <view class="empty-icon">💬</view>
      <text class="empty-text">暂无评价</text>
      <text class="empty-desc">快来成为第一个评价的人吧~</text>
      <view class="empty-action">
        <button class="write-comment-btn" bindtap="goToWriteComment">
          <text class="btn-text">写评价</text>
        </button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && comments.length > 0}}">
      <view class="load-more-btn" bindtap="loadMore" wx:if="{{!loadingMore}}">
        <text class="load-more-text">加载更多</text>
      </view>
      <view class="loading-more" wx:if="{{loadingMore}}">
        <text class="loading-more-text">加载中...</text>
      </view>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && comments.length > 0}}">
      <text class="no-more-text">没有更多评价了</text>
    </view>
  </view>
</view>