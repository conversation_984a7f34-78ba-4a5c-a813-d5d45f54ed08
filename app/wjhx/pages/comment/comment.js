var app = getApp();
var util = require('../../utils/util.js');
var api = require('../../config/api.js');

Page({
    data: {
        // 基础数据
        comments: [],
        allCommentList: [],
        picCommentList: [],
        goodCommentList: [],
        normalCommentList: [],
        badCommentList: [],

        // 页面参数
        typeId: 0,
        valueId: 0,
        showType: 0, // 0:全部 1:有图 2:好评 3:中评 4:差评

        // 统计数据
        allCount: 0,
        hasPicCount: 0,
        goodCount: 0,
        normalCount: 0,
        badCount: 0,
        averageRating: 0,
        averageRatingDisplay: '暂无评分',
        averageStarData: [],
        satisfactionRate: 100,

        // 分页数据
        currentPage: 1,
        pageSize: 20,
        hasMore: true,

        // 状态控制
        loading: false,
        loadingMore: false,
        refreshing: false
    },

    // 标准化评分数据
    normalizeRating: function(rating) {
        // 处理各种可能的输入类型
        if (rating === null || rating === undefined || rating === '' || rating === 'null') {
            return this.getDefaultRating(); // 使用动态默认值
        }

        // 转换为数字
        let numRating;
        if (typeof rating === 'string') {
            numRating = parseFloat(rating.trim());
        } else if (typeof rating === 'number') {
            numRating = rating;
        } else {
            return this.getDefaultRating(); // 无效类型使用默认值
        }

        // 检查是否为有效数字
        if (isNaN(numRating) || !isFinite(numRating)) {
            return this.getDefaultRating(); // 无效数据使用默认值
        }

        // 限制在1-5范围内，保留2位小数
        const clampedRating = Math.max(1, Math.min(5, numRating));
        return Math.round(clampedRating * 100) / 100; // 保留2位小数
    },

    // 获取默认评分（根据是否有评论数据动态决定）
    getDefaultRating: function() {
        const allCount = this.data.allCount || 0;
        if (allCount === 0) {
            return 0; // 无评论时显示0分
        }
        return 5.0; // 有评论但无平均分时显示5分
    },

    // 格式化评分显示
    formatRatingDisplay: function(rating) {
        const normalizedRating = this.normalizeRating(rating);
        if (normalizedRating === 0) {
            return '暂无评分';
        }
        return normalizedRating.toFixed(1); // 显示1位小数
    },

    // 获取星星评分的CSS类名
    getRatingStarClass: function(index, rating) {
        const numRating = this.normalizeRating(rating);
        const starIndex = index + 1; // 星星从1开始计数

        console.log(`getRatingStarClass: index=${index}, rating=${rating}, numRating=${numRating}, starIndex=${starIndex}`);

        if (starIndex <= numRating) {
            return 'active'; // 有填充的星星
        } else {
            return ''; // 空星
        }
    },

    // 获取星星填充宽度百分比
    getRatingStarWidth: function(index, rating) {
        const numRating = this.normalizeRating(rating);
        const starIndex = index + 1; // 星星从1开始计数

        console.log(`getRatingStarWidth: index=${index}, rating=${rating}, numRating=${numRating}, starIndex=${starIndex}`);

        if (starIndex <= Math.floor(numRating)) {
            return 100; // 完整星星
        } else if (starIndex === Math.ceil(numRating) && numRating % 1 !== 0) {
            return (numRating % 1) * 100; // 部分星星
        } else {
            return 0; // 空星
        }
    },

    // 计算星星显示数据
    calculateStarData: function(rating) {
        const numRating = this.normalizeRating(rating);
        const stars = [];

        for (let i = 0; i < 5; i++) {
            const starIndex = i + 1;
            let width = 0;
            let active = false;

            if (starIndex <= Math.floor(numRating)) {
                width = 100; // 完整星星
                active = true;
            } else if (starIndex === Math.ceil(numRating) && numRating % 1 !== 0) {
                width = (numRating % 1) * 100; // 部分星星
                active = true;
            } else {
                width = 0; // 空星
                active = false;
            }

            stars.push({
                index: i,
                width: width,
                active: active
            });
        }

        return stars;
    },

    // 获取评论统计数据
    getCommentCount: function () {
        const that = this;

        // 检查缓存
        const cacheKey = `comment_stats_${that.data.typeId}_${that.data.valueId}`;
        const cachedData = that.getCachedStats(cacheKey);
        if (cachedData) {
            that.processCommentStats(cachedData);
            return;
        }

        util.request(api.CommentCount, {
            valueId: that.data.valueId,
            typeId: that.data.typeId
        }).then(function (res) {
            // 兼容不同的响应格式
            const success = res.success || res.errno === 0;
            const responseData = res.data || {};

            if (success) {
                // 缓存数据（5分钟有效期）
                that.setCachedStats(cacheKey, responseData, 5 * 60 * 1000);

                // 处理统计数据
                that.processCommentStats(responseData);
            } else {
                console.error('获取评论统计失败:', res.errmsg || res.message);
                that.handleCommentStatsError(res.errmsg || res.message || '获取评论统计失败');
            }
        }).catch(function (error) {
            console.error('获取评论统计失败:', error);
            that.handleCommentStatsError('网络错误，请重试');
        });
    },

    // 处理评论统计数据
    processCommentStats: function(responseData) {
        const that = this;

        // 处理评论数量数据
        const allCount = parseInt(responseData.allCount) || 0;
        const hasPicCount = parseInt(responseData.hasPicCount) || 0;
        const goodCount = parseInt(responseData.goodCount) || 0;
        const normalCount = parseInt(responseData.normalCount) || 0;
        const badCount = parseInt(responseData.badCount) || 0;

        // 处理平均评分
        const averageRating = that.processAverageRating(responseData.averageRating, allCount);

        // 处理好评率
        const satisfactionRate = that.processSatisfactionRate(responseData.satisfactionRate, goodCount, allCount);

        // 更新页面数据
        that.setData({
            allCount: allCount,
            hasPicCount: hasPicCount,
            goodCount: goodCount,
            normalCount: normalCount,
            badCount: badCount,
            averageRating: averageRating,
            averageRatingDisplay: that.formatRatingDisplay(averageRating),
            averageStarData: that.calculateStarData(averageRating),
            satisfactionRate: satisfactionRate
        });

        console.log('评论统计数据处理完成:', {
            allCount,
            averageRating,
            satisfactionRate
        });
    },

    // 处理平均评分数据
    processAverageRating: function(averageRating, allCount) {
        // 如果没有评论，返回0
        if (allCount === 0) {
            return 0;
        }

        // 标准化评分数据
        const normalizedRating = this.normalizeRating(averageRating);

        // 如果标准化后仍然是默认值且有评论，可能是数据异常
        if (normalizedRating === this.getDefaultRating() && allCount > 0) {
            console.warn('平均评分数据异常，使用默认值:', {
                originalRating: averageRating,
                allCount: allCount,
                normalizedRating: normalizedRating
            });
        }

        return normalizedRating;
    },

    // 处理好评率数据
    processSatisfactionRate: function(satisfactionRate, goodCount, allCount) {
        // 如果没有评论，返回100%
        if (allCount === 0) {
            return 100;
        }

        // 如果有传入好评率，使用传入值
        if (satisfactionRate !== null && satisfactionRate !== undefined && satisfactionRate !== '') {
            const rate = parseFloat(satisfactionRate);
            if (!isNaN(rate) && isFinite(rate)) {
                return Math.max(0, Math.min(100, Math.round(rate)));
            }
        }

        // 根据好评数量计算好评率
        if (goodCount >= 0 && allCount > 0) {
            const calculatedRate = (goodCount / allCount) * 100;
            return Math.round(calculatedRate);
        }

        // 默认返回100%
        return 100;
    },

    // 处理评论统计错误
    handleCommentStatsError: function(errorMessage) {
        const that = this;

        // 设置默认数据
        that.setData({
            allCount: 0,
            hasPicCount: 0,
            goodCount: 0,
            normalCount: 0,
            badCount: 0,
            averageRating: 0,
            averageRatingDisplay: '暂无评分',
            averageStarData: that.calculateStarData(0),
            satisfactionRate: 100
        });

        // 显示错误提示
        util.showErrorToast(errorMessage);
    },

    // 获取缓存的统计数据
    getCachedStats: function(cacheKey) {
        try {
            const cachedStr = wx.getStorageSync(cacheKey);
            if (cachedStr) {
                const cached = JSON.parse(cachedStr);
                const now = Date.now();

                // 检查是否过期
                if (cached.expireTime && now < cached.expireTime) {
                    console.log('使用缓存的评论统计数据:', cacheKey);
                    return cached.data;
                } else {
                    // 过期则删除缓存
                    wx.removeStorageSync(cacheKey);
                }
            }
        } catch (error) {
            console.error('获取缓存数据失败:', error);
            // 清除可能损坏的缓存
            wx.removeStorageSync(cacheKey);
        }
        return null;
    },

    // 设置缓存的统计数据
    setCachedStats: function(cacheKey, data, ttl) {
        try {
            const cacheData = {
                data: data,
                expireTime: Date.now() + ttl,
                createTime: Date.now()
            };
            wx.setStorageSync(cacheKey, JSON.stringify(cacheData));
            console.log('缓存评论统计数据:', cacheKey, 'TTL:', ttl);
        } catch (error) {
            console.error('设置缓存数据失败:', error);
        }
    },

    // 清除评论统计缓存
    clearCommentStatsCache: function() {
        const cacheKey = `comment_stats_${this.data.typeId}_${this.data.valueId}`;
        try {
            wx.removeStorageSync(cacheKey);
            console.log('清除评论统计缓存:', cacheKey);
        } catch (error) {
            console.error('清除缓存失败:', error);
        }
    },
    // 获取评论列表
    getCommentList: function (isRefresh = false) {
        const that = this;

        // 设置加载状态
        if (isRefresh) {
            that.setData({ refreshing: true });
        } else if (that.data.currentPage === 1) {
            that.setData({ loading: true });
        } else {
            that.setData({ loadingMore: true });
        }

        const requestData = {
            valueId: that.data.valueId,
            typeId: that.data.typeId,
            pageSize: that.data.pageSize,
            pageNum: that.data.currentPage,
            requirePicture: that.data.showType === 1 // 当showType为1时需要图片
        };

        console.log('评论列表请求参数:', requestData);
        console.log('API地址:', api.CommentList);

        util.request(api.CommentList, requestData).then(function (res) {
            // 兼容不同的响应格式
            const success = res.success || res.errno === 0;
            const responseData = res.data || [];

            if (success) {
                const newComments = Array.isArray(responseData) ? responseData : (responseData.data || []);
                let updatedComments = [];

                if (isRefresh || that.data.currentPage === 1) {
                    // 刷新或首次加载
                    updatedComments = newComments;
                } else {
                    // 加载更多
                    updatedComments = that.data.comments.concat(newComments);
                }

                // 处理评论数据
                const processedComments = that.processCommentData(updatedComments);

                that.setData({
                    comments: processedComments,
                    hasMore: newComments.length >= that.data.pageSize,
                    loading: false,
                    loadingMore: false,
                    refreshing: false
                });

                // 缓存不同类型的评论列表
                that.cacheCommentsByType(processedComments);
            } else {
                console.error('获取评论列表失败:', res.errmsg || res.message);
                that.setData({
                    loading: false,
                    loadingMore: false,
                    refreshing: false
                });
                util.showErrorToast(res.errmsg || res.message || '获取评论失败');
            }
        }).catch(function (error) {
            console.error('获取评论列表失败:', error);
            that.setData({
                loading: false,
                loadingMore: false,
                refreshing: false
            });
            util.showErrorToast('获取评论失败');
        });
    },

    // 处理评论数据
    processCommentData: function (comments) {
        const that = this; // 保存this上下文
        return comments.map(comment => {
            // 格式化时间
            if (comment.createTime) {
                comment.createTime = util.formatTime(new Date(comment.createTime));
            }

            // 确保用户信息存在
            if (!comment.userInfo) {
                comment.userInfo = {
                    nickname: '匿名用户',
                    avatar: 'https://via.placeholder.com/80x80/f0f0f0/999999?text=头像'
                };
            } else {
                // 处理头像为空的情况
                if (!comment.userInfo.avatar || comment.userInfo.avatar === '') {
                    comment.userInfo.avatar = 'https://via.placeholder.com/80x80/f0f0f0/999999?text=头像';
                }
                // 处理昵称为空的情况
                if (!comment.userInfo.nickname || comment.userInfo.nickname === '') {
                    comment.userInfo.nickname = '匿名用户';
                }
            }

            // 确保图片列表存在
            if (!comment.picList) {
                comment.picList = [];
            }

            // 处理评分数据
            const originalRating = comment.rating;
            comment.rating = that.normalizeRating(comment.rating);

            // 预计算星星显示数据
            comment.starData = that.calculateStarData(comment.rating);

            console.log("comment rating processed==>", comment.rating, "original:", originalRating, "starData:", comment.starData);
            return comment;
        });
    },

    // 缓存不同类型的评论
    cacheCommentsByType: function (comments) {
        const that = this;
        const showType = that.data.showType;

        switch (showType) {
            case 0: // 全部
                that.setData({ allCommentList: comments });
                break;
            case 1: // 有图
                that.setData({ picCommentList: comments });
                break;
        }
    },
    onLoad: function (options) {
        // 页面初始化
        this.setData({
            typeId: parseInt(options.typeId) || 0,
            valueId: parseInt(options.valueId) || 0,
            averageRating: 0,
            averageRatingDisplay: '暂无评分',
            averageStarData: this.calculateStarData(0) // 初始化为0星
        });

        // 初始化数据
        this.initData();
    },

    // 初始化数据
    initData: function () {
        this.getCommentCount();
        this.getCommentList();
    },
    onReady: function () {
        // 页面渲染完成
    },

    onShow: function () {
        // 页面显示
    },

    onHide: function () {
        // 页面隐藏
    },

    onUnload: function () {
        // 页面关闭
    },

    // 下拉刷新
    onPullDownRefresh: function () {
        this.refreshComments();
    },
    // 切换标签
    switchTab: function (e) {
        const type = e.currentTarget.dataset.type;
        const newShowType = type !== undefined ? parseInt(type) : (this.data.showType === 0 ? 1 : 0);

        if (newShowType === this.data.showType) {
            return; // 相同标签不处理
        }

        this.setData({
            showType: newShowType,
            currentPage: 1,
            hasMore: true,
            comments: [] // 清空当前评论列表
        });

        // 重新获取评论列表
        this.getCommentList();
    },

    // 获取缓存的评论
    getCachedComments: function (type) {
        switch (type) {
            case 0: return this.data.allCommentList;
            case 1: return this.data.picCommentList;
            default: return [];
        }
    },

    // 触底加载更多
    onReachBottom: function () {
        if (!this.data.hasMore || this.data.loadingMore) {
            return;
        }
        this.loadMore();
    },

    // 加载更多
    loadMore: function () {
        this.setData({
            currentPage: this.data.currentPage + 1
        });
        this.getCommentList();
    },

    // 刷新评论
    refreshComments: function () {
        // 清除缓存，强制重新获取数据
        this.clearCommentStatsCache();

        this.setData({
            currentPage: 1,
            hasMore: true
        });

        // 重新获取统计数据和评论列表
        this.getCommentCount();
        this.getCommentList(true);

        // 停止下拉刷新
        setTimeout(() => {
            wx.stopPullDownRefresh();
        }, 1000);
    },

    // 返回上一页
    goBack: function () {
        wx.navigateBack({
            delta: 1
        });
    },

    // 预览图片
    previewImage: function (e) {
        const current = e.currentTarget.dataset.current;
        const urls = e.currentTarget.dataset.urls;

        wx.previewImage({
            current: current,
            urls: urls
        });
    },

    // 点赞/取消点赞
    toggleLike: function (e) {
        const commentId = e.currentTarget.dataset.id;
        const that = this;

        // 找到对应的评论
        const comments = that.data.comments;
        const commentIndex = comments.findIndex(item => item.id === commentId);

        if (commentIndex === -1) return;

        const comment = comments[commentIndex];
        const isLiked = comment.isLiked;
        const newLikeCount = isLiked ? (comment.likeCount || 1) - 1 : (comment.likeCount || 0) + 1;

        // 乐观更新UI
        comment.isLiked = !isLiked;
        comment.likeCount = newLikeCount;

        that.setData({
            [`comments[${commentIndex}]`]: comment
        });

        // 调用API
        util.request(api.CommentLike || (api.ApiRootUrl + 'comment/like'), {
            commentId: commentId,
            action: isLiked ? 'unlike' : 'like'
        }, 'POST').then(function (res) {
            if (!res.success) {
                // 如果失败，回滚UI状态
                comment.isLiked = isLiked;
                comment.likeCount = isLiked ? newLikeCount + 1 : newLikeCount - 1;
                that.setData({
                    [`comments[${commentIndex}]`]: comment
                });
                util.showErrorToast('操作失败');
            }
        }).catch(function (error) {
            // 网络错误，回滚UI状态
            comment.isLiked = isLiked;
            comment.likeCount = isLiked ? newLikeCount + 1 : newLikeCount - 1;
            that.setData({
                [`comments[${commentIndex}]`]: comment
            });
            util.showErrorToast('网络错误');
        });
    },

    // 显示回复输入框
    showReplyInput: function (e) {
        const commentId = e.currentTarget.dataset.id;

        wx.showModal({
            title: '回复评论',
            content: '此功能正在开发中，敬请期待！',
            showCancel: false,
            confirmText: '知道了'
        });
    },

    // 跳转到写评价页面
    goToWriteComment: function() {
        wx.navigateTo({
            url: `/pages/commentPost/commentPost?typeId=${this.data.typeId}&valueId=${this.data.valueId}`
        });
    },

    // 分享评论
    shareComment: function(e) {
        const commentId = e.currentTarget.dataset.id;
        wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
        });
    }
})