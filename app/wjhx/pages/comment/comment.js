var app = getApp();
var util = require('../../utils/util.js');
var api = require('../../config/api.js');

Page({
    data: {
        // 基础数据
        comments: [],
        allCommentList: [],
        picCommentList: [],
        goodCommentList: [],
        normalCommentList: [],
        badCommentList: [],

        // 页面参数
        typeId: 0,
        valueId: 0,
        showType: 0, // 0:全部 1:有图 2:好评 3:中评 4:差评

        // 统计数据
        allCount: 0,
        hasPicCount: 0,
        goodCount: 0,
        normalCount: 0,
        badCount: 0,
        averageRating: 5.0,
        satisfactionRate: 100,

        // 分页数据
        currentPage: 1,
        pageSize: 20,
        hasMore: true,

        // 状态控制
        loading: false,
        loadingMore: false,
        refreshing: false
    },

    // 标准化评分数据
    normalizeRating: function(rating) {
        if (rating === null || rating === undefined || rating === '') {
            return 5; // 默认5星
        }

        const numRating = parseFloat(rating);
        if (isNaN(numRating)) {
            return 5; // 无效数据默认5星
        }

        // 限制在1-5范围内
        return Math.max(1, Math.min(5, numRating));
    },

    // 获取星星评分的CSS类名
    getRatingStarClass: function(index, rating) {
        const numRating = this.normalizeRating(rating);
        const starIndex = index + 1; // 星星从1开始计数

        if (starIndex <= numRating) {
            return 'active'; // 有填充的星星
        } else {
            return ''; // 空星
        }
    },

    // 获取星星填充宽度百分比
    getRatingStarWidth: function(index, rating) {
        const numRating = this.normalizeRating(rating);
        const starIndex = index + 1; // 星星从1开始计数

        if (starIndex <= Math.floor(numRating)) {
            return 100; // 完整星星
        } else if (starIndex === Math.ceil(numRating) && numRating % 1 !== 0) {
            return (numRating % 1) * 100; // 部分星星
        } else {
            return 0; // 空星
        }
    },

    // 获取评论统计数据
    getCommentCount: function () {
        const that = this;

        util.request(api.CommentCount, {
            valueId: that.data.valueId,
            typeId: that.data.typeId
        }).then(function (res) {
            // 兼容不同的响应格式
            const success = res.success || res.errno === 0;
            const responseData = res.data || {};

            if (success) {
                that.setData({
                    allCount: responseData.allCount || 0,
                    hasPicCount: responseData.hasPicCount || 0,
                    goodCount: responseData.goodCount || 0,
                    normalCount: responseData.normalCount || 0,
                    badCount: responseData.badCount || 0,
                    averageRating: that.normalizeRating(responseData.averageRating) || 5.0,
                    satisfactionRate: responseData.satisfactionRate || 100
                });
            } else {
                console.error('获取评论统计失败:', res.errmsg || res.message);
                util.showErrorToast(res.errmsg || res.message || '获取评论统计失败');
            }
        }).catch(function (error) {
            console.error('获取评论统计失败:', error);
            util.showErrorToast('获取评论统计失败');
        });
    },
    // 获取评论列表
    getCommentList: function (isRefresh = false) {
        const that = this;

        // 设置加载状态
        if (isRefresh) {
            that.setData({ refreshing: true });
        } else if (that.data.currentPage === 1) {
            that.setData({ loading: true });
        } else {
            that.setData({ loadingMore: true });
        }

        const requestData = {
            valueId: that.data.valueId,
            typeId: that.data.typeId,
            pageSize: that.data.pageSize,
            pageNum: that.data.currentPage,
            requirePicture: that.data.showType === 1 // 当showType为1时需要图片
        };

        console.log('评论列表请求参数:', requestData);
        console.log('API地址:', api.CommentList);

        util.request(api.CommentList, requestData).then(function (res) {
            // 兼容不同的响应格式
            const success = res.success || res.errno === 0;
            const responseData = res.data || [];

            if (success) {
                const newComments = Array.isArray(responseData) ? responseData : (responseData.data || []);
                let updatedComments = [];

                if (isRefresh || that.data.currentPage === 1) {
                    // 刷新或首次加载
                    updatedComments = newComments;
                } else {
                    // 加载更多
                    updatedComments = that.data.comments.concat(newComments);
                }

                // 处理评论数据
                const processedComments = that.processCommentData(updatedComments);

                that.setData({
                    comments: processedComments,
                    hasMore: newComments.length >= that.data.pageSize,
                    loading: false,
                    loadingMore: false,
                    refreshing: false
                });

                // 缓存不同类型的评论列表
                that.cacheCommentsByType(processedComments);
            } else {
                console.error('获取评论列表失败:', res.errmsg || res.message);
                that.setData({
                    loading: false,
                    loadingMore: false,
                    refreshing: false
                });
                util.showErrorToast(res.errmsg || res.message || '获取评论失败');
            }
        }).catch(function (error) {
            console.error('获取评论列表失败:', error);
            that.setData({
                loading: false,
                loadingMore: false,
                refreshing: false
            });
            util.showErrorToast('获取评论失败');
        });
    },

    // 处理评论数据
    processCommentData: function (comments) {
        return comments.map(comment => {
            // 格式化时间
            if (comment.createTime) {
                comment.createTime = util.formatTime(new Date(comment.createTime));
            }

            // 确保用户信息存在
            if (!comment.userInfo) {
                comment.userInfo = {
                    nickname: '匿名用户',
                    avatar: 'https://via.placeholder.com/80x80/f0f0f0/999999?text=头像'
                };
            } else {
                // 处理头像为空的情况
                if (!comment.userInfo.avatar || comment.userInfo.avatar === '') {
                    comment.userInfo.avatar = 'https://via.placeholder.com/80x80/f0f0f0/999999?text=头像';
                }
                // 处理昵称为空的情况
                if (!comment.userInfo.nickname || comment.userInfo.nickname === '') {
                    comment.userInfo.nickname = '匿名用户';
                }
            }

            // 确保图片列表存在
            if (!comment.picList) {
                comment.picList = [];
            }

            // 处理评分数据
            comment.rating = this.normalizeRating(comment.rating);
            console.log("comment==>",comment)
            return comment;
        });
    },

    // 缓存不同类型的评论
    cacheCommentsByType: function (comments) {
        const that = this;
        const showType = that.data.showType;

        switch (showType) {
            case 0: // 全部
                that.setData({ allCommentList: comments });
                break;
            case 1: // 有图
                that.setData({ picCommentList: comments });
                break;
        }
    },
    onLoad: function (options) {
        // 页面初始化
        this.setData({
            typeId: parseInt(options.typeId) || 0,
            valueId: parseInt(options.valueId) || 0
        });

        // 初始化数据
        this.initData();
    },

    // 初始化数据
    initData: function () {
        this.getCommentCount();
        this.getCommentList();
    },
    onReady: function () {
        // 页面渲染完成
    },

    onShow: function () {
        // 页面显示
    },

    onHide: function () {
        // 页面隐藏
    },

    onUnload: function () {
        // 页面关闭
    },

    // 下拉刷新
    onPullDownRefresh: function () {
        this.refreshComments();
    },
    // 切换标签
    switchTab: function (e) {
        const type = e.currentTarget.dataset.type;
        const newShowType = type !== undefined ? parseInt(type) : (this.data.showType === 0 ? 1 : 0);

        if (newShowType === this.data.showType) {
            return; // 相同标签不处理
        }

        this.setData({
            showType: newShowType,
            currentPage: 1,
            hasMore: true,
            comments: [] // 清空当前评论列表
        });

        // 重新获取评论列表
        this.getCommentList();
    },

    // 获取缓存的评论
    getCachedComments: function (type) {
        switch (type) {
            case 0: return this.data.allCommentList;
            case 1: return this.data.picCommentList;
            default: return [];
        }
    },

    // 触底加载更多
    onReachBottom: function () {
        if (!this.data.hasMore || this.data.loadingMore) {
            return;
        }
        this.loadMore();
    },

    // 加载更多
    loadMore: function () {
        this.setData({
            currentPage: this.data.currentPage + 1
        });
        this.getCommentList();
    },

    // 刷新评论
    refreshComments: function () {
        this.setData({
            currentPage: 1,
            hasMore: true
        });
        this.getCommentList(true);

        // 停止下拉刷新
        setTimeout(() => {
            wx.stopPullDownRefresh();
        }, 1000);
    },

    // 返回上一页
    goBack: function () {
        wx.navigateBack({
            delta: 1
        });
    },

    // 预览图片
    previewImage: function (e) {
        const current = e.currentTarget.dataset.current;
        const urls = e.currentTarget.dataset.urls;

        wx.previewImage({
            current: current,
            urls: urls
        });
    },

    // 点赞/取消点赞
    toggleLike: function (e) {
        const commentId = e.currentTarget.dataset.id;
        const that = this;

        // 找到对应的评论
        const comments = that.data.comments;
        const commentIndex = comments.findIndex(item => item.id === commentId);

        if (commentIndex === -1) return;

        const comment = comments[commentIndex];
        const isLiked = comment.isLiked;
        const newLikeCount = isLiked ? (comment.likeCount || 1) - 1 : (comment.likeCount || 0) + 1;

        // 乐观更新UI
        comment.isLiked = !isLiked;
        comment.likeCount = newLikeCount;

        that.setData({
            [`comments[${commentIndex}]`]: comment
        });

        // 调用API
        util.request(api.CommentLike || (api.ApiRootUrl + 'comment/like'), {
            commentId: commentId,
            action: isLiked ? 'unlike' : 'like'
        }, 'POST').then(function (res) {
            if (!res.success) {
                // 如果失败，回滚UI状态
                comment.isLiked = isLiked;
                comment.likeCount = isLiked ? newLikeCount + 1 : newLikeCount - 1;
                that.setData({
                    [`comments[${commentIndex}]`]: comment
                });
                util.showErrorToast('操作失败');
            }
        }).catch(function (error) {
            // 网络错误，回滚UI状态
            comment.isLiked = isLiked;
            comment.likeCount = isLiked ? newLikeCount + 1 : newLikeCount - 1;
            that.setData({
                [`comments[${commentIndex}]`]: comment
            });
            util.showErrorToast('网络错误');
        });
    },

    // 显示回复输入框
    showReplyInput: function (e) {
        const commentId = e.currentTarget.dataset.id;

        wx.showModal({
            title: '回复评论',
            content: '此功能正在开发中，敬请期待！',
            showCancel: false,
            confirmText: '知道了'
        });
    },

    // 跳转到写评价页面
    goToWriteComment: function() {
        wx.navigateTo({
            url: `/pages/commentPost/commentPost?typeId=${this.data.typeId}&valueId=${this.data.valueId}`
        });
    },

    // 分享评论
    shareComment: function(e) {
        const commentId = e.currentTarget.dataset.id;
        wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
        });
    }
})