/* 评论页面样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
}

.comment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 60rpx;
}

/* 评价统计 */
.comment-stats {
  background: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.stats-header {
  display: flex;
  align-items: center;
}

.total-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}

.rating-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star-container {
  position: relative;
  display: inline-block;
}

.star {
  font-size: 24rpx;
  line-height: 1;
  display: block;
  width: 24rpx;
  height: 24rpx;
  text-align: center;
}

.star-bg {
  color: #ddd;
  position: relative;
  z-index: 1;
}

.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  color: #ffb400;
  z-index: 2;
  white-space: nowrap;
}

.star-fill.active {
  color: #ffb400;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.total-count {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.satisfaction {
  font-size: 28rpx;
  color: #666;
}

/* 筛选标签 */
.filter-tabs {
  background: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.tab-list {
  display: flex;
  padding: 0 30rpx;
  gap: 20rpx;
  justify-content: center;
}

.tab-item {
  flex-shrink: 0;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #ff6b6b;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
}

.tab-item.active .tab-text {
  color: #fff;
  font-weight: 500;
}

/* 评论列表 */
.comment-list {
  padding: 0 30rpx 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-animation {
  display: flex;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ff6b6b;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.dot1 { animation-delay: -0.32s; }
.dot2 { animation-delay: -0.16s; }
.dot3 { animation-delay: 0s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 评论项 */
.comment-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.comment-header {
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: flex-start;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.user-detail {
  flex: 1;
}

.user-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
}

.rating-stars .star-container {
  position: relative;
  display: inline-block;
}

.rating-stars .star {
  font-size: 24rpx;
  line-height: 1;
  display: block;
  width: 24rpx;
  height: 24rpx;
  text-align: center;
}

.rating-stars .star-bg {
  color: #ddd;
  position: relative;
  z-index: 1;
}

.rating-stars .star-fill {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  color: #ffb400;
  z-index: 2;
  white-space: nowrap;
}

.rating-stars .star-fill.active {
  color: #ffb400;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

/* 评论内容 */
.comment-content {
  margin-bottom: 20rpx;
}

.comment-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

/* 评价图片 */
.comment-images {
  margin-bottom: 20rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.comment-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
}

/* 商品规格 */
.product-spec {
  margin-bottom: 20rpx;
}

.spec-text {
  font-size: 26rpx;
  color: #999;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  display: inline-block;
}

/* 商家回复 */
.merchant-reply {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #ff6b6b;
}

.reply-header {
  margin-bottom: 8rpx;
}

.reply-label {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: 500;
}

.reply-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.comment-actions {
  display: flex;
  gap: 40rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.action-btn.liked {
  background: #ffe6e6;
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.action-btn.liked .action-text {
  color: #ff6b6b;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}

.empty-action {
  margin-top: 20rpx;
}

.write-comment-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  border: none;
  border-radius: 32rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.btn-text {
  color: #fff;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.load-more-btn {
  padding: 16rpx 40rpx;
  background: #fff;
  border: 1rpx solid #e5e5e5;
  border-radius: 32rpx;
  color: #666;
  font-size: 28rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 12rpx;
  color: #999;
  font-size: 28rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .comment-item {
    padding: 24rpx;
  }

  .user-avatar {
    width: 70rpx;
    height: 70rpx;
  }

  .comment-image {
    width: 140rpx;
    height: 140rpx;
  }
}