/* ===== 页面基础样式 ===== */
page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    color: #333333;
}

.page-wrapper {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f8f9fa 30%);
    padding-bottom: 160rpx; /* 增加底部padding，确保内容不被支付栏遮挡 */
}

/* ===== 顶部区域样式 ===== */
.header-section {
    position: relative;
    padding: 60rpx 30rpx 40rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

.header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.deco-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatShape 8s ease-in-out infinite;
}

.shape-1 {
    width: 200rpx;
    height: 200rpx;
    top: -50rpx;
    right: -50rpx;
    animation-delay: 0s;
}

.shape-2 {
    width: 120rpx;
    height: 120rpx;
    top: 100rpx;
    left: -30rpx;
    animation-delay: 3s;
}

.page-title {
    text-align: center;
    position: relative;
    z-index: 2;
}

.title-text {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8rpx;
    letter-spacing: 2rpx;
}

.title-subtitle {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 1rpx;
}

/* ===== 主要内容区域 ===== */
.main-content {
    flex: 1;
    padding: 24rpx 20rpx 60rpx; /* 增加底部padding，确保费用明细完全显示 */
    background: #f8f9fa;
    border-radius: 32rpx 32rpx 0 0;
    margin-top: -32rpx;
    position: relative;
    z-index: 8;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* ===== 通用卡片样式 ===== */
.address-card,
.coupon-card,
.goods-card,
.price-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.9);
    transition: all 200ms ease-in-out;
}

/* 费用明细卡片特殊样式 - 增加底部间距 */
.price-card {
    margin-bottom: 72rpx; /* 进一步增加底部间距，从48rpx增加到72rpx */
}

.card-header {
    display: flex;
    align-items: center;
    padding: 24rpx 24rpx 16rpx;
    background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
    border-bottom: 2rpx solid #f5f6fa;
}

.header-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
}

.header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    flex: 1;
}

.goods-count {
    font-size: 24rpx;
    color: #8e8e93;
    background: rgba(142, 142, 147, 0.1);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
}

/* ===== 地址卡片样式 ===== */
.address-content {
    display: flex;
    align-items: center;
    padding: 24rpx;
    transition: all 200ms ease-in-out;
}

.address-content:active {
    background: #f8f9fa;
}

.address-info {
    flex: 1;
    min-width: 0;
}

.recipient-info {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    gap: 16rpx;
}

.recipient-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
}

.recipient-phone {
    font-size: 28rpx;
    color: #666666;
}

.default-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffffff;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-weight: 500;
}

.address-detail {
    margin-bottom: 8rpx;
}

.address-text {
    font-size: 26rpx;
    color: #666666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}

.address-arrow {
    flex-shrink: 0;
    margin-left: 16rpx;
}

.arrow-icon {
    font-size: 32rpx;
    color: #cccccc;
}

/* ===== 空地址状态 ===== */
.address-empty {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    transition: all 200ms ease-in-out;
}

.address-empty:active {
    background: #f8f9fa;
}

.empty-icon {
    font-size: 48rpx;
    margin-right: 20rpx;
    opacity: 0.6;
}

.empty-content {
    flex: 1;
}

.empty-title {
    font-size: 30rpx;
    color: #333333;
    font-weight: 500;
    margin-bottom: 4rpx;
}

.empty-desc {
    font-size: 24rpx;
    color: #8e8e93;
}

.add-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffffff;
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    font-size: 26rpx;
    font-weight: 500;
}

/* ===== 优惠券卡片样式 ===== */
.coupon-content {
    display: flex;
    align-items: center;
    padding: 24rpx;
    transition: all 200ms ease-in-out;
}

.coupon-content:active {
    background: #f8f9fa;
}

.coupon-info {
    flex: 1;
}

.coupon-title {
    font-size: 30rpx;
    color: #333333;
    font-weight: 500;
    margin-bottom: 4rpx;
}

.coupon-title.selected {
    color: #667eea;
    font-weight: 600;
}

.coupon-count {
    font-size: 24rpx;
    color: #8e8e93;
}

.coupon-arrow {
    flex-shrink: 0;
    margin-left: 16rpx;
}

/* ===== 积分抵扣卡片样式 ===== */
.points-card,
.balance-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.9);
    transition: all 200ms ease-in-out;
}

.points-balance,
.balance-amount {
    font-size: 24rpx;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    font-weight: 500;
}

.points-content,
.balance-content {
    padding: 24rpx;
}

.points-switch,
.balance-switch {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.switch-label {
    font-size: 30rpx;
    color: #333333;
    font-weight: 500;
}

.points-input-section,
.balance-input-section {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 20rpx;
    margin-top: 16rpx;
}

.points-input-wrapper,
.balance-input-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
}

.input-label {
    font-size: 28rpx;
    color: #666666;
    margin-right: 16rpx;
    flex-shrink: 0;
}

.points-input,
.balance-input {
    flex: 1;
    background: #ffffff;
    border: 2rpx solid #e5e5ea;
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    font-size: 28rpx;
    color: #333333;
    margin-right: 12rpx;
}

.points-input:focus,
.balance-input:focus {
    border-color: #667eea;
}

.input-unit {
    font-size: 26rpx;
    color: #8e8e93;
    flex-shrink: 0;
}

.points-tips,
.balance-tips {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tip-text {
    font-size: 24rpx;
    color: #8e8e93;
    flex: 1;
}

.max-use-btn {
    font-size: 24rpx;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    font-weight: 500;
    flex-shrink: 0;
}

.max-use-btn:active {
    background: rgba(102, 126, 234, 0.2);
}

/* ===== 积分/余额禁用状态样式 ===== */
.points-disabled-info,
.balance-disabled-info {
    padding: 16rpx 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    margin-top: 16rpx;
}

.disabled-text {
    font-size: 24rpx;
    color: #8e8e93;
    text-align: center;
    display: block;
}

/* ===== 商品列表样式 ===== */
.goods-list {
    padding: 0 24rpx 16rpx;
}

.goods-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f5f6fa;
}

.goods-item:last-child {
    border-bottom: none;
}

.goods-image-wrapper {
    width: 120rpx;
    height: 120rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.goods-image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    background: #f0f0f0;
}

.goods-info {
    flex: 1;
    min-width: 0;
}

.goods-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8rpx;
}

.goods-name {
    flex: 1;
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
    line-height: 1.4;
    margin-right: 16rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}

/* ===== 商品数量调整控件样式 ===== */
.goods-quantity-control {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 20rpx;
    padding: 4rpx;
    flex-shrink: 0;
    border: 1rpx solid #e5e5ea;
}

.quantity-btn {
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 16rpx;
    transition: all 200ms ease-in-out;
    user-select: none;
    cursor: pointer;
}

.quantity-minus {
    background: #ffffff;
    color: #667eea;
    border: 1rpx solid #e5e5ea;
}

.quantity-minus:active {
    background: #f0f2ff;
    transform: scale(0.95);
}

.quantity-minus.disabled {
    background: #f5f5f5;
    color: #cccccc;
    cursor: not-allowed;
}

.quantity-plus {
    background: #667eea;
    color: #ffffff;
    border: 1rpx solid #667eea;
}

.quantity-plus:active {
    background: #5a6fd8;
    transform: scale(0.95);
}

.quantity-display {
    width: 80rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    background: transparent;
    margin: 0 8rpx;
    user-select: none;
}

/* ===== 商品价格信息样式优化 ===== */
.goods-price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8rpx;
}

.goods-price {
    font-size: 30rpx;
    color: #ff6b6b;
    font-weight: 600;
}

.goods-subtotal {
    font-size: 24rpx;
    color: #8e8e93;
    font-weight: 500;
}

.goods-spec {
    font-size: 24rpx;
    color: #8e8e93;
    margin-bottom: 8rpx;
    background: rgba(142, 142, 147, 0.1);
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    display: inline-block;
}

.goods-price {
    font-size: 30rpx;
    color: #ff6b6b;
    font-weight: 600;
}

/* ===== 价格明细样式 ===== */
.price-details {
    padding: 16rpx 24rpx 24rpx;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
}

.price-item:last-child {
    margin-bottom: 0;
}

.price-label {
    font-size: 28rpx;
    color: #666666;
    font-weight: 400;
}

.price-value {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

.discount-item .price-label {
    color: #ff6b6b;
}

.discount-value {
    color: #ff6b6b !important;
    font-weight: 600;
}

.price-divider {
    height: 1rpx;
    background: #f0f0f0;
    margin: 16rpx 0;
}

/* ===== 运费提醒样式 ===== */
.freight-notice {
    display: flex;
    align-items: flex-start;
    padding: 12rpx 16rpx;
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-radius: 8rpx;
    margin: 12rpx 0 8rpx 0;
    border-left: 4rpx solid #ff9800;
}

.notice-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
    margin-top: 2rpx;
    flex-shrink: 0;
}

.notice-text {
    font-size: 24rpx;
    color: #e65100;
    line-height: 1.4;
    flex: 1;
}

.total-item {
    margin-top: 8rpx;
}

.total-item .price-label {
    font-size: 32rpx;
    color: #333333;
    font-weight: 600;
}

.total-value {
    font-size: 36rpx;
    color: #ff6b6b !important;
    font-weight: 700;
}

/* ===== 底部支付栏样式 ===== */
.payment-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 20rpx 24rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid #f0f0f0;
    display: flex;
    align-items: center;
    z-index: 1000;
}

.payment-info {
    flex: 1;
    margin-right: 20rpx;
}

.total-price {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.price-label {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 4rpx;
}

.price-amount {
    display: flex;
    align-items: baseline;
    gap: 4rpx;
}

.currency-symbol {
    font-size: 28rpx;
    color: #ff6b6b;
    font-weight: 600;
}

.amount-value {
    font-size: 42rpx;
    color: #ff6b6b;
    font-weight: 700;
    letter-spacing: 1rpx;
}

.payment-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffffff;
    padding: 20rpx 40rpx;
    border-radius: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
    transition: all 200ms ease-in-out;
    min-width: 200rpx;
    text-align: center;
}

.payment-button:active {
    transform: scale(0.95);
    box-shadow: 0 3rpx 10rpx rgba(102, 126, 234, 0.5);
}

.button-text {
    letter-spacing: 2rpx;
}

/* ===== 动画效果 ===== */
@keyframes floatShape {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-30rpx) rotate(180deg);
        opacity: 0.8;
    }
}

/* ===== 响应式设计 ===== */
@media screen and (max-width: 750rpx) {
    .header-section {
        padding: 50rpx 20rpx 30rpx;
    }

    .title-text {
        font-size: 42rpx;
    }

    .title-subtitle {
        font-size: 22rpx;
    }

    .main-content {
        padding: 20rpx 16rpx 60rpx; /* 确保小屏幕下也有足够的底部空间 */
    }

    .address-card,
    .coupon-card,
    .goods-card,
    .price-card {
        margin-bottom: 20rpx;
        border-radius: 20rpx;
    }

    /* 小屏幕下费用明细卡片增加底部间距 */
    .price-card {
        margin-bottom: 60rpx; /* 小屏幕下进一步增加底部间距，从40rpx增加到60rpx */
    }

  
    .card-header {
        padding: 20rpx 20rpx 12rpx;
    }

    .header-title {
        font-size: 28rpx;
    }

    .address-content,
    .coupon-content {
        padding: 20rpx;
    }

    .recipient-name {
        font-size: 28rpx;
    }

    .recipient-phone {
        font-size: 26rpx;
    }

    .goods-list {
        padding: 0 20rpx 12rpx;
    }

    .goods-image-wrapper {
        width: 100rpx;
        height: 100rpx;
    }

    .goods-name {
        font-size: 26rpx;
    }

    .price-details {
        padding: 12rpx 20rpx 20rpx;
    }

    .payment-bar {
        padding: 16rpx 20rpx;
    }

    .amount-value {
        font-size: 36rpx;
    }

    .payment-button {
        padding: 16rpx 32rpx;
        font-size: 28rpx;
        min-width: 160rpx;
    }

    .deco-shape {
        opacity: 0.5;
    }

    .shape-1 {
        width: 150rpx;
        height: 150rpx;
    }

    .shape-2 {
        width: 100rpx;
        height: 100rpx;
    }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
    .header-section {
        padding: 40rpx 16rpx 24rpx;
    }

    .title-text {
        font-size: 36rpx;
    }

    .main-content {
        padding: 16rpx 12rpx 60rpx; /* 确保超小屏幕下也有足够的底部空间 */
    }

    /* 超小屏幕下费用明细卡片增加底部间距 */
    .price-card {
        margin-bottom: 48rpx; /* 超小屏幕下进一步增加底部间距，从36rpx增加到48rpx */
    }

    .card-header {
        padding: 16rpx 16rpx 8rpx;
    }

    .address-content,
    .coupon-content {
        padding: 16rpx;
    }

    .goods-list {
        padding: 0 16rpx 8rpx;
    }

    .price-details {
        padding: 8rpx 16rpx 16rpx;
    }

    .payment-bar {
        padding: 12rpx 16rpx;
    }

    .amount-value {
        font-size: 32rpx;
    }

    .payment-button {
        padding: 12rpx 24rpx;
        font-size: 26rpx;
        min-width: 140rpx;
    }

    .deco-shape {
        display: none;
    }
}

/* ===== 组合抵扣提示样式 ===== */
.combo-discount-tip {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    margin: 16rpx 0;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    border-radius: 16rpx;
    border: 2rpx solid rgba(255, 154, 158, 0.3);
    box-shadow: 0 4rpx 12rpx rgba(255, 154, 158, 0.2);
    animation: gentlePulse 2s ease-in-out infinite;
}

.combo-discount-tip .tip-icon {
    font-size: 32rpx;
    margin-right: 12rpx;
    animation: bounce 1.5s ease-in-out infinite;
}

.combo-discount-tip .tip-text {
    font-size: 24rpx;
    color: #d63384;
    font-weight: 500;
    line-height: 1.4;
    flex: 1;
}

/* 动画效果 */
@keyframes gentlePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4rpx 12rpx rgba(255, 154, 158, 0.2);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 6rpx 16rpx rgba(255, 154, 158, 0.3);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-6rpx);
    }
    60% {
        transform: translateY(-3rpx);
    }
}

/* ===== 一键优惠按钮样式 ===== */
.quick-discount-card {
    margin: 0 30rpx 20rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
    overflow: hidden;
    position: relative;
}

.quick-discount-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

.quick-discount-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 30rpx;
    position: relative;
    z-index: 1;
}

.quick-discount-info {
    flex: 1;
}

.quick-discount-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8rpx;
}

.quick-discount-desc {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.3;
}

.quick-discount-button {
    background: rgba(255, 255, 255, 0.2);
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 50rpx;
    padding: 16rpx 32rpx;
    backdrop-filter: blur(10rpx);
    transition: all 0.3s ease;
}

.quick-discount-button:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
}

.quick-discount-button .button-text {
    font-size: 26rpx;
    font-weight: 500;
    color: #ffffff;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
