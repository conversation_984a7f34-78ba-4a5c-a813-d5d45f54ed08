<!-- allGoods.wxml -->
<wxs src="../../utils/format.wxs" module="format" />
<view class="container">
  <!-- Loading Error -->
  <view class="error-view" wx:if="{{errorMessage}}">
    <image class="error-icon" src="../../static/images/icon_error.png"></image>
    <text class="error-text">{{errorMessage}}</text>
    <view class="error-btn" bindtap="loadGoodsList">重新加载</view>
  </view>
  <view wx:else>
    <!-- Search and Filter Bar -->
    <view class="search-filter-bar">
      <view class="search-container">
        <view class="search-box {{searchFocused ? 'focused' : ''}}">
          <image class="search-icon" src="../../static/images/search.png"></image>
          <input class="search-input" placeholder="搜索商品" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearchConfirm" bindfocus="onSearchFocus" bindblur="onSearchBlur" confirm-type="search" />
          <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="clearSearch">
            <image src="../../static/images/icon_close.png"></image>
          </view>
        </view>
      </view>
      <view class="filter-container">
        <view class="filter-btn" bindtap="showSortModal">
          <text class="filter-text">{{sortText}}</text>
          <image class="filter-icon" src="../../static/images/icon/right-btn.png"></image>
        </view>
      </view>
    </view>
    <!-- Category Filter -->
    <!-- <view class="category-filter" wx:if="{{categoryList.length > 0}}" >
      <scroll-view scroll-x="true" class="category-scroll" show-scrollbar="false">
        <view class="category-item {{currentCategoryId === 0 ? 'active' : ''}}"
              bindtap="selectCategory" data-id="0">
          全部
        </view>
        <view class="category-item {{currentCategoryId === item.id ? 'active' : ''}}"
              wx:for="{{categoryList}}" wx:key="id"
              bindtap="selectCategory" data-id="{{item.id}}">
          {{item.name}}
        </view>
      </scroll-view>
    </view> -->
    <!-- Search Result Info -->
    <view class="search-result-info" wx:if="{{searchKeyword && goodsList.length > 0}}">
      <text class="result-text">搜索"{{searchKeyword}}"找到 {{totalSearchResults}} 个商品</text>
      <text class="page-info" wx:if="{{totalSearchResults > goodsList.length}}">已显示 {{goodsList.length}} 个</text>
    </view>
    <!-- Goods List -->
    <view class="goods-container">
      <view class="goods-row">
        <view class="goods-item" wx:for="{{goodsList}}" wx:key="id">
          <navigator url="../goods/goods?id={{item.id}}" class="goods-link">
            <!-- Product Image -->
            <view class="img-container">
              <image class="goods-img" src="{{format.formatImageUrl(item.listPicUrl, 'thumb')}}" mode="aspectFill" lazy-load="true" binderror="imageError" data-index="{{index}}" show-menu-by-longpress="{{false}}"></image>
              <view wx:if="{{item.isHot}}" class="promo-tag hot-tag">热卖</view>
              <view wx:if="{{item.isNew}}" class="promo-tag new-tag">新品</view>
            </view>
            <!-- Product Info -->
            <view class="goods-info">
              <!-- Title -->
              <view class="goods-title">{{item.name}}</view>
              <!-- Brief Description -->
              <view class="goods-brief">{{item.goodsBrief || '精选商品 品质保障'}}</view>
              <!-- Price and Cart -->
              <view class="price-cart">
                <view class="goods-price-info">
                  <text class="symbol">￥</text>
                  <text class="price-num">{{item.unitPrice || item.retailPrice}}</text>
                  <text class="original-price" wx:if="{{item.counterPrice && item.counterPrice > item.retailPrice}}">
                    ￥{{item.counterPrice}}
                  </text>
                </view>
                <view class="sales-info"></view>
                <!-- <view class="sales-info">已售{{item.sellVolume || Math.floor(Math.random() * 1000 + 10)}}件</view> -->
                <view class="cart-icon" catchtap="addToCart" data-goods-id="{{item.id}}">
                  <image src="../../static/images/ic_menu_shoping_nor.png"></image>
                </view>
              </view>
              <!-- Tags -->
              <view class="goods-tags">
                <text class="tag official-tag">产地直发</text>
                <text class="tag" wx:if="{{item.isHot}}">热销</text>
                <text class="tag" wx:if="{{item.isNew}}">新品</text>
                <text class="tag">包邮</text>
              </view>
            </view>
          </navigator>
        </view>
      </view>
    </view>
    <!-- Loading States -->
    <view class="load-more" wx:if="{{goodsList.length > 0 && loading && !noMore}}">加载中...</view>
    <view class="no-more" wx:if="{{goodsList.length > 0 && noMore}}">已经到底了</view>
    <view class="empty-tip" wx:if="{{goodsList.length === 0 && !loading && !errorMessage}}">
      <text wx:if="{{searchKeyword}}">未找到"{{searchKeyword}}"相关商品</text>
      <text wx:else>暂无商品数据</text>
    </view>
  </view>
  <!-- Sort Modal -->
  <view class="modal-overlay" wx:if="{{showSortModal}}" bindtap="hideSortModal">
    <view class="sort-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">排序方式</text>
        <view class="modal-close" bindtap="hideSortModal">×</view>
      </view>
      <view class="sort-options">
        <view class="sort-option {{sortType === 'default' ? 'active' : ''}}" bindtap="selectSort" data-type="default">
          <text>默认排序</text>
          <image wx:if="{{sortType === 'default'}}" class="check-icon" src="../../static/images/checkbox.png"></image>
        </view>
        <view class="sort-option {{sortType === 'price_asc' ? 'active' : ''}}" bindtap="selectSort" data-type="price_asc">
          <text>价格从低到高</text>
          <image wx:if="{{sortType === 'price_asc'}}" class="check-icon" src="../../static/images/checkbox.png"></image>
        </view>
        <view class="sort-option {{sortType === 'price_desc' ? 'active' : ''}}" bindtap="selectSort" data-type="price_desc">
          <text>价格从高到低</text>
          <image wx:if="{{sortType === 'price_desc'}}" class="check-icon" src="../../static/images/checkbox.png"></image>
        </view>
        <!-- <view class="sort-option {{sortType === 'sales' ? 'active' : ''}}"
              bindtap="selectSort" data-type="sales">
          <text>销量优先</text>
          <image wx:if="{{sortType === 'sales'}}" class="check-icon" src="../../static/images/checkbox.png"></image>
        </view> -->
      </view>
    </view>
  </view>
  <!-- 飞入购物车动画元素 -->
  <view class="fly-item" hidden="{{!openFly}}" style="top:{{flyTop}}px;left:{{flyLeft}}px;width:{{flyWidth}}px;height:{{flyHeight}}px;animation-duration:{{flyTime}}s">
    <image class="fly-img" src="{{flyImg}}"></image>
  </view>
</view>