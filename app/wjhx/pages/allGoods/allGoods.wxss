/* 防止页面左右拖动 */
page{
  box-sizing: border-box;
}

page, .container, view {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Error View */
.error-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 60rpx;
  min-height: 60vh;
}

.error-icon {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 48rpx;
  opacity: 0.7;
  filter: grayscale(20%);
  transition: all 200ms ease;
}

.error-view:active .error-icon {
  transform: scale(1.05);
  opacity: 0.8;
}

.error-text {
  font-size: 32rpx;
  color: #64748b;
  text-align: center;
  margin-bottom: 48rpx;
  font-weight: 500;
  line-height: 1.6;
  max-width: 400rpx;
}

.error-btn {
  padding: 24rpx 72rpx;
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 600;
  border-radius: 48rpx;
  box-shadow: 0 8rpx 24rpx rgba(66, 165, 245, 0.35);
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.error-btn:active {
  transform: scale(0.98) translateY(1rpx);
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.4);
}

page {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

.container {
  width: 100%;
  max-width: 100vw;
  min-height: 100vh;
  background: transparent;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* Search and Filter Bar */
.search-filter-bar {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  overflow: hidden;
}

.search-container {
  flex: 1;
  margin-right: 24rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(66, 165, 245, 0.1);
  border-radius: 56rpx;
  padding: 20rpx 32rpx;
  height: 80rpx;
  box-sizing: border-box;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}

.search-box.focused {
  border-color: #42A5F5;
  background: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.15);
  transform: translateY(-1rpx);
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
  opacity: 0.7;
  transition: opacity 200ms ease;
}

.search-box.focused .search-icon {
  opacity: 1;
}

.search-input {
  flex: 1;
  font-size: 30rpx;
  color: #1e293b;
  font-weight: 400;
  background: transparent;
  border: none;
  outline: none;
  height: 100%;
}

.search-input::placeholder {
  color: #94a3b8;
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 50%;
  margin-left: 16rpx;
  transition: all 200ms ease;
}

.search-clear:active {
  background: rgba(148, 163, 184, 0.2);
  transform: scale(0.95);
}

.search-clear image {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.6;
}

.filter-container {
  flex-shrink: 0;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(66, 165, 245, 0.1);
  border-radius: 56rpx;
  height: 80rpx;
  box-sizing: border-box;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.filter-btn:active {
  border-color: #42A5F5;
  background: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.15);
  transform: scale(0.98);
}

.filter-text {
  font-size: 28rpx;
  color: #475569;
  margin-right: 12rpx;
  font-weight: 500;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.7;
  transition: transform 200ms ease;
}

.filter-btn:active .filter-icon {
  transform: rotate(90deg);
}

/* Search Result Info */
.search-result-info {
  padding: 16rpx 32rpx;
  background: rgba(66, 165, 245, 0.05);
  border-left: 4rpx solid #42A5F5;
  margin: 0 20rpx 16rpx 20rpx;
  border-radius: 8rpx;
}

.result-text {
  font-size: 26rpx;
  color: #42A5F5;
  font-weight: 500;
  margin-right: 16rpx;
}

.page-info {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 400;
}

/* Category Filter */
.category-filter {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24rpx 0 36rpx 0;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  overflow: hidden;
}

.category-scroll {
  white-space: nowrap;
  padding: 0 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.category-item {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 28rpx;
  margin-right: 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #475569;
  font-weight: 500;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
}

.category-item.active {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  border-color: transparent;
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.25);
  transform: translateY(-1rpx);
}

.category-item:active {
  transform: scale(0.98) translateY(-1rpx);
}

.category-item.active:active {
  transform: scale(0.98);
}

/* Goods Container */
.goods-container {
  width: 100%;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  box-sizing: border-box;
  flex: 1;
  overflow: hidden;
}

.goods-row {
  width: 100%;
  max-width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  padding-bottom: 32rpx;
  box-sizing: border-box;
}

.goods-item {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.goods-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  opacity: 0;
  transition: opacity 200ms ease;
  pointer-events: none;
  z-index: 1;
}

.goods-item:active::before {
  opacity: 1;
}

.goods-item:active {
  transform: scale(0.98) translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.goods-link {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.img-container {
  width: 100%;
  height: 280rpx;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0;
  box-sizing: border-box;
}

.goods-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 200ms ease;
}

.goods-item:active .goods-img {
  transform: scale(1.05);
}

.promo-tag {
  position: absolute;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  color: #ffffff;
  z-index: 10;
  border-radius: 0 0 12rpx 0;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.hot-tag {
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.new-tag {
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.goods-info {
  padding: 16rpx 20rpx 20rpx 20rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  z-index: 2;
}

.goods-title {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 78rpx;
  letter-spacing: 0.3rpx;
}

.goods-brief {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.3;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
}

.price-cart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 8rpx;
}

.goods-price-info {
  display: flex;
  align-items: baseline;
  flex: 1;
}

.symbol {
  font-size: 24rpx;
  color: #ef4444;
  margin-right: 4rpx;
  font-weight: 600;
}

.price-num {
  font-size: 32rpx;
  color: #ef4444;
  font-weight: 700;
  margin-right: 8rpx;
  letter-spacing: 0.5rpx;
}

.original-price {
  font-size: 22rpx;
  color: #94a3b8;
  text-decoration: line-through;
  font-weight: 400;
}

.sales-info {
  font-size: 22rpx;
  color: #64748b;
  margin-right: auto;
  margin-left: 12rpx;
  font-weight: 500;
}

.cart-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(6, 182, 212, 0.25);
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.cart-icon:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(6, 182, 212, 0.3);
}

.cart-icon image {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(10);
  transition: transform 200ms ease;
}

.cart-icon:active image {
  transform: scale(1.1);
}

.goods-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  margin-top: auto;
}

.tag {
  font-size: 20rpx;
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  font-weight: 500;
  border: 1rpx solid rgba(239, 68, 68, 0.2);
  transition: all 200ms ease;
}

.official-tag {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: #ffffff;
  border-color: transparent;
  box-shadow: 0 2rpx 4rpx rgba(6, 182, 212, 0.2);
}

/* Loading States */
.load-more, .no-more, .empty-tip {
  text-align: center;
  font-size: 28rpx;
  color: #64748b;
  padding: 48rpx 0;
  font-weight: 500;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.load-more::before {
  content: '';
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(66, 165, 245, 0.2);
  border-top: 3rpx solid #42A5F5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.no-more {
  color: #94a3b8;
  position: relative;
}

.no-more::before,
.no-more::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1rpx;
  background: #e2e8f0;
}

.no-more::before {
  left: -80rpx;
}

.no-more::after {
  right: -80rpx;
}

.empty-tip {
  color: #94a3b8;
  font-size: 32rpx;
  padding: 120rpx 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Sort Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 200ms ease-out;
  overflow: hidden;
}

.sort-modal {
  width: 100%;
  max-width: 100vw;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  animation: slideUp 200ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 36rpx 40rpx;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.5rpx;
}

.modal-close {
  font-size: 48rpx;
  color: #64748b;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 200ms ease;
}

.modal-close:active {
  background: rgba(100, 116, 139, 0.1);
  color: #475569;
  transform: scale(0.95);
}

.sort-options {
  padding: 24rpx 0 40rpx 0;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx;
  font-size: 32rpx;
  color: #475569;
  font-weight: 500;
  transition: all 200ms ease;
  position: relative;
}

.sort-option.active {
  color: #42A5F5;
  background: rgba(66, 165, 245, 0.05);
}

.sort-option.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
}

.sort-option:active {
  background: rgba(248, 250, 252, 0.8);
  transform: scale(0.98);
}

.check-icon {
  width: 36rpx;
  height: 36rpx;
  transition: transform 200ms ease;
}

.sort-option.active .check-icon {
  transform: scale(1.1);
}

/* 响应式优化 */
@media (max-width: 375px) {
  .goods-row {
    gap: 16rpx;
    width: 100%;
    max-width: 100%;
  }

  .goods-item {
    border-radius: 16rpx;
    width: 100%;
    max-width: 100%;
  }

  .img-container {
    height: 260rpx;
  }

  .goods-info {
    padding: 14rpx 18rpx 18rpx 18rpx;
  }

  .goods-title {
    font-size: 26rpx;
    height: 72rpx;
    margin-bottom: 6rpx;
  }

  .goods-brief {
    font-size: 22rpx;
    margin-bottom: 10rpx;
  }

  .price-num {
    font-size: 30rpx;
  }

  .sales-info {
    font-size: 20rpx;
  }

  .cart-icon {
    width: 44rpx;
    height: 44rpx;
  }

  .cart-icon image {
    width: 26rpx;
    height: 26rpx;
  }

  .tag {
    font-size: 18rpx;
    padding: 3rpx 8rpx;
  }
}

/* Flying animation for adding to cart */
.fly-item {
  position: fixed;
  z-index: 999;
  border-radius: 50%;
  overflow: hidden;
  animation: flyToCart 0.8s ease-in-out forwards;
  pointer-events: none;
  background: #fff;
  box-shadow: 0 0 10rpx rgba(0,0,0,0.2);
}

.fly-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

@keyframes flyToCart {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  20% {
    transform: scale(1.2) rotate(5deg);
    opacity: 0.9;
  }
  60% {
    transform: scale(0.6) rotate(-5deg);
    opacity: 0.6;
  }
  100% {
    transform: scale(0.2) rotate(0deg);
    opacity: 0;
    top: 90% !important;
    left: 80% !important;
  }
}
