const util = require('../../utils/util.js');
const api = require('../../config/api.js');
const cartService = require('../../services/cart.js');

Page({
  data: {
    goodsList: [],
    categoryList: [],
    currentCategoryId: 0,
    page: 1,
    pageSize: 10, // 减少每页加载数量从20改为10
    loading: false,
    noMore: false,
    errorMessage: '',

    // 排序相关
    sortType: 'default', // default, price_asc, price_desc, sales
    sortText: '默认排序',
    showSortModal: false,

    // 飞入购物车动画相关
    openFly: false,
    flyImg: '',
    flyTop: 0,
    flyLeft: 0,
    flyWidth: 50,
    flyHeight: 50,
    flyTime: 0.8,

    // 搜索相关
    searchKeyword: '',
    searchFocused: false,
    searchTimer: null,
    totalSearchResults: 0, // 搜索结果总数
    
    // 添加图片加载状态
    imageLoadStates: {}
  },

  onLoad: function(options) {
    // 从参数中获取分类ID和搜索关键词
    if (options.categoryId) {
      this.setData({
        currentCategoryId: parseInt(options.categoryId)
      });
    }
    if (options.keyword) {
      this.setData({
        searchKeyword: options.keyword
      });
    }

    this.loadCategoryList();
    this.loadGoodsList(true);
  },

  onShow: function() {
    // 页面显示时刷新数据
  },

  onUnload: function() {
    // 页面卸载时清理定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
  },

  onPullDownRefresh: function() {
    this.loadGoodsList(true);
  },

  onReachBottom: function() {
    if (!this.data.loading && !this.data.noMore) {
      this.loadGoodsList(false);
    }
  },

  // 加载分类列表
  loadCategoryList: function() {
    const that = this;
    util.request(api.CatalogList).then(function(res) {
      if (res.success && res.data.categoryList) {
        that.setData({
          categoryList: res.data.categoryList
        });
      }
    }).catch(function(error) {
      console.log('加载分类失败:', error);
    });
  },

  // 加载商品列表
  loadGoodsList: function(refresh = false) {
    const that = this;

    if (refresh) {
      this.setData({
        page: 1,
        goodsList: [],
        noMore: false,
        errorMessage: '',
        totalSearchResults: 0
      });
    }

    this.setData({ loading: true });

    // 构建请求参数
    const params = {
      pageNum: this.data.page,
      pageSize: this.data.pageSize
    };

    // 添加分类筛选
    if (this.data.currentCategoryId > 0) {
      params.categoryId = this.data.currentCategoryId;
    }

    // 添加搜索关键词
    if (this.data.searchKeyword) {
      params.keyword = this.data.searchKeyword;
    }

    // 添加排序参数
    if (this.data.sortType !== 'default') {
      switch(this.data.sortType) {
        case 'price_asc':
          params.sort = 'price';
          params.order = 'asc';
          break;
        case 'price_desc':
          params.sort = 'price';
          params.order = 'desc';
          break;
        case 'sales':
          params.sort = 'sales';
          params.order = 'desc';
          break;
        default:
          // 默认排序不传参数
          break;
      }
    }

    console.log('商品列表请求参数:', params);

    util.request(api.GoodsList, params).then(function(res) {
      that.setData({ loading: false });

      if (refresh) {
        wx.stopPullDownRefresh();
      }

      if (res.success) {
        const newGoodsList = res.data.goodsList || [];
        let allGoodsList = [];

        if (refresh) {
          allGoodsList = newGoodsList;
        } else {
          allGoodsList = that.data.goodsList.concat(newGoodsList);
        }

        // 构建商品分类ID到名称的映射
        const productTypes = {};
        if (res.data.categoryList && res.data.categoryList.length > 0) {
          res.data.categoryList.forEach(category => {
            if (category.id && category.name) {
              productTypes[category.id] = category.name;
            }
          });
        }

        // 为商品添加分类信息
        allGoodsList.forEach(item => {
          if (!item.goodsType && item.categoryId && productTypes[item.categoryId]) {
            item.goodsType = productTypes[item.categoryId];
          }
        });

        // 临时前端排序解决方案（在后端修复之前）
        if (that.data.sortType !== 'default' && refresh) {
          allGoodsList = that.sortGoodsList(allGoodsList, that.data.sortType);
        }

        // 更新搜索结果总数
        let totalResults = that.data.totalSearchResults;
        if (refresh) {
          // 如果是刷新，使用后端返回的总数或当前商品数量
          totalResults = res.data.total || res.data.totalCount || allGoodsList.length;
        } else {
          // 如果是分页加载，累加当前页面的商品数量
          totalResults = allGoodsList.length;
        }

        that.setData({
          goodsList: allGoodsList,
          page: that.data.page + 1,
          noMore: newGoodsList.length < that.data.pageSize,
          totalSearchResults: totalResults
        });
      } else {
        that.setData({
          errorMessage: res.message || '加载失败'
        });
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    }).catch(function(error) {
      that.setData({
        loading: false,
        errorMessage: '网络异常，请重试'
      });

      if (refresh) {
        wx.stopPullDownRefresh();
      }

      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  // 选择分类
  selectCategory: function(e) {
    const categoryId = parseInt(e.currentTarget.dataset.id);
    if (categoryId !== this.data.currentCategoryId) {
      this.setData({
        currentCategoryId: categoryId
      });
      this.loadGoodsList(true);
    }
  },

  // 搜索输入处理
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }

    // 设置新的定时器，延迟搜索
    const timer = setTimeout(() => {
      this.performSearch();
    }, 500); // 500ms延迟搜索

    this.setData({
      searchTimer: timer
    });
  },

  // 搜索确认
  onSearchConfirm: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    this.performSearch();
  },

  // 搜索框获得焦点
  onSearchFocus: function() {
    this.setData({
      searchFocused: true
    });
  },

  // 搜索框失去焦点
  onSearchBlur: function() {
    this.setData({
      searchFocused: false
    });
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      totalSearchResults: 0
    });
    this.performSearch();
  },

  // 执行搜索
  performSearch: function() {
    // 清除定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
      this.setData({
        searchTimer: null
      });
    }

    console.log('执行搜索，关键词:', this.data.searchKeyword);
    this.loadGoodsList(true);
  },

  // 显示排序模态框
  showSortModal: function() {
    this.setData({
      showSortModal: true
    });
  },

  // 隐藏排序模态框
  hideSortModal: function() {
    this.setData({
      showSortModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  },

  // 选择排序方式
  selectSort: function(e) {
    const sortType = e.currentTarget.dataset.type;
    let sortText = '默认排序';

    switch(sortType) {
      case 'price_asc':
        sortText = '价格从低到高';
        break;
      case 'price_desc':
        sortText = '价格从高到低';
        break;
      case 'sales':
        sortText = '销量优先';
        break;
      default:
        sortText = '默认排序';
    }

    console.log('选择排序方式:', sortType, sortText);

    this.setData({
      sortType: sortType,
      sortText: sortText,
      showSortModal: false
    });

    this.loadGoodsList(true);
  },

  // 处理图片加载错误
  imageError: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('图片加载失败:', index);
    
    // 使用默认图片替换
    const goodsList = this.data.goodsList;
    if (goodsList[index]) {
      goodsList[index].listPicUrl = '/static/images/goods1.jpg';
      this.setData({
        goodsList: goodsList
      });
    }
  },

  // 添加到购物车
  addToCart: function(e) {
    const goodsId = e.currentTarget.dataset.goodsId;
    let that = this;

    // 检查用户登录状态
    if (!wx.getStorageSync('userInfo')) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    // 获取点击位置信息用于动画
    if (that.data.goodsList && that.data.goodsList.length > 0) {
      cartService.getFlyAnimation(e, goodsId, that.data.goodsList, that);
    }

    cartService.addToCart({
      goodsId: goodsId,
      number: 1,
      directAdd: false, // 让cartService检查是否需要选择规格
      success: function (res) {
        console.log('添加到购物车成功:', res);
        // 可以在这里更新购物车数量显示
        that.updateCartCount();
      },
      fail: function (error) {
        console.error('添加到购物车失败:', error);
      }
    });
  },

  // 更新购物车数量显示
  updateCartCount: function() {
    let that = this;
    util.request(api.CartList).then(function (res) {
      if (res.success && res.data && res.data.cartList) {
        const cartCount = res.data.cartList.reduce((total, item) => total + item.number, 0);
        // 可以在这里设置购物车数量到页面数据中
        // that.setData({ cartCount: cartCount });
      }
    }).catch(function (error) {
      console.error('获取购物车数量失败:', error);
    });
  },

  // 格式化分享图片URL，确保符合微信分享要求
  formatShareImageUrl: function(url) {
    if (!url) {
      return '';
    }

    // 使用项目的formatImageUrl函数
    let formattedUrl = util.formatImageUrl(url);

    // 如果是localhost，需要替换为实际的域名
    if (formattedUrl.includes('localhost:9999')) {
      // 使用配置文件中的域名
      formattedUrl = formattedUrl.replace('http://localhost:9999', 'https://www.sxwjsm.com');
    }

    // 确保使用HTTPS协议（微信分享要求）
    if (formattedUrl.startsWith('http://')) {
      formattedUrl = formattedUrl.replace('http://', 'https://');
    }

    return formattedUrl;
  },

  onShareAppMessage: function() {
    let shareImageUrl = '';

    // 获取第一个商品的图片作为分享图片
    if (this.data.goodsList && this.data.goodsList.length > 0) {
      const firstGoods = this.data.goodsList[0];
      if (firstGoods.listPicUrl) {
        shareImageUrl = this.formatShareImageUrl(firstGoods.listPicUrl);
      }
    }

    return {
      title: '所有商品 - 伍俊惠选',
      desc: '精选商品，品质保障',
      path: '/pages/allGoods/allGoods',
      imageUrl: shareImageUrl
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function() {
    let shareImageUrl = '';
    let shareTitle = '伍俊惠选 - 精选商品';

    // 根据当前页面状态生成分享内容
    if (this.data.searchKeyword) {
      shareTitle = `"${this.data.searchKeyword}" 搜索结果 - 伍俊惠选`;
    } else if (this.data.currentCategoryId > 0) {
      // 查找分类名称
      const currentCategory = this.data.categoryList.find(cat => cat.id === this.data.currentCategoryId);
      if (currentCategory) {
        shareTitle = `${currentCategory.name} - 伍俊惠选`;
      }
    } else {
      shareTitle = '精选商品 - 伍俊惠选';
    }

    // 获取第一个商品的图片作为分享图片
    if (this.data.goodsList && this.data.goodsList.length > 0) {
      const firstGoods = this.data.goodsList[0];
      if (firstGoods.listPicUrl) {
        shareImageUrl = this.formatShareImageUrl(firstGoods.listPicUrl);
      }
    }

    console.log('分享到朋友圈 - 标题:', shareTitle);
    console.log('分享到朋友圈 - 图片URL:', shareImageUrl);

    // 构建查询参数
    let query = '';
    const queryParams = [];

    if (this.data.currentCategoryId > 0) {
      queryParams.push(`categoryId=${this.data.currentCategoryId}`);
    }
    if (this.data.searchKeyword) {
      queryParams.push(`keyword=${encodeURIComponent(this.data.searchKeyword)}`);
    }

    if (queryParams.length > 0) {
      query = queryParams.join('&');
    }

    return {
      title: shareTitle,
      query: query,
      imageUrl: shareImageUrl
    };
  },

  // 临时前端排序方法（在后端修复之前使用）
  sortGoodsList: function(goodsList, sortType) {
    const sortedList = [...goodsList];

    switch(sortType) {
      case 'price_asc':
        return sortedList.sort((a, b) => {
          const priceA = parseFloat(a.unitPrice || a.retailPrice) || 0;
          const priceB = parseFloat(b.unitPrice || b.retailPrice) || 0;
          return priceA - priceB;
        });
      case 'price_desc':
        return sortedList.sort((a, b) => {
          const priceA = parseFloat(a.unitPrice || a.retailPrice) || 0;
          const priceB = parseFloat(b.unitPrice || b.retailPrice) || 0;
          return priceB - priceA;
        });
      case 'sales':
        return sortedList.sort((a, b) => {
          const salesA = parseInt(a.sellVolume) || Math.floor(Math.random() * 1000 + 10);
          const salesB = parseInt(b.sellVolume) || Math.floor(Math.random() * 1000 + 10);
          return salesB - salesA;
        });
      default:
        return sortedList;
    }
  }
});
