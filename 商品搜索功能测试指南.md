# 商品搜索功能测试指南

## 测试环境准备
1. 确保小程序开发工具已打开allGoods页面
2. 确保后端商品数据接口正常
3. 准备测试用的商品关键词

## 功能测试用例

### 1. 搜索输入框基础功能测试

#### 测试步骤：
1. 打开allGoods页面
2. 点击搜索输入框
3. 输入商品关键词（如"苹果"）
4. 观察搜索框状态变化

#### 预期结果：
- 搜索框获得焦点时边框变蓝色
- 输入内容正常显示
- 出现清除按钮（×）

### 2. 延迟搜索功能测试

#### 测试步骤：
1. 在搜索框中输入关键词
2. 停止输入，等待500ms
3. 观察商品列表变化

#### 预期结果：
- 500ms后自动触发搜索
- 商品列表更新为搜索结果
- 显示搜索结果数量提示

### 3. 即时搜索功能测试

#### 测试步骤：
1. 在搜索框中输入关键词
2. 按键盘确认键或搜索键
3. 观察商品列表变化

#### 预期结果：
- 立即触发搜索
- 商品列表更新为搜索结果
- 显示搜索结果数量提示

### 4. 清除搜索功能测试

#### 测试步骤：
1. 输入搜索关键词并搜索
2. 点击搜索框右侧的清除按钮（×）
3. 观察页面变化

#### 预期结果：
- 搜索框内容清空
- 商品列表恢复显示所有商品
- 搜索结果提示消失

### 5. 搜索结果展示测试

#### 测试步骤：
1. 搜索存在的商品关键词
2. 搜索不存在的商品关键词
3. 观察结果展示

#### 预期结果：
- 有结果时显示："搜索'关键词'找到 X 个商品"
- 无结果时显示："未找到'关键词'相关商品"

### 6. 搜索与排序功能兼容性测试

#### 测试步骤：
1. 输入搜索关键词
2. 点击排序按钮，选择不同排序方式
3. 观察搜索结果是否按选择的方式排序

#### 预期结果：
- 搜索结果能正确按价格排序
- 搜索状态保持不变

### 7. 搜索与分页功能兼容性测试

#### 测试步骤：
1. 搜索有多页结果的关键词
2. 向下滚动触发分页加载
3. 观察加载的内容

#### 预期结果：
- 分页加载的仍是搜索结果
- 搜索状态保持不变

## 性能测试

### 1. 频繁输入测试
#### 测试步骤：
1. 快速连续输入多个字符
2. 观察网络请求情况

#### 预期结果：
- 不会产生过多的网络请求
- 延迟搜索机制正常工作

### 2. 内存泄漏测试
#### 测试步骤：
1. 多次进入和退出allGoods页面
2. 在页面中进行多次搜索操作
3. 观察内存使用情况

#### 预期结果：
- 页面退出时定时器被正确清理
- 无内存泄漏现象

## 边界情况测试

### 1. 空搜索测试
#### 测试步骤：
1. 在搜索框中输入空格或特殊字符
2. 触发搜索

#### 预期结果：
- 能正确处理空搜索
- 不会出现错误

### 2. 长关键词测试
#### 测试步骤：
1. 输入很长的搜索关键词
2. 观察界面显示

#### 预期结果：
- 搜索框能正确显示长文本
- 搜索结果提示能正确显示

### 3. 网络异常测试
#### 测试步骤：
1. 断开网络连接
2. 进行搜索操作
3. 恢复网络连接

#### 预期结果：
- 网络异常时显示错误提示
- 网络恢复后能正常搜索

## 用户体验测试

### 1. 响应速度测试
- 搜索框输入响应是否流畅
- 搜索结果加载速度是否合理

### 2. 视觉反馈测试
- 搜索框焦点状态是否明显
- 清除按钮是否易于识别和点击

### 3. 操作便利性测试
- 搜索操作是否直观
- 清除搜索是否方便

## 测试数据建议

### 常用搜索关键词：
- "苹果" - 测试水果类商品
- "手机" - 测试电子产品
- "衣服" - 测试服装类商品
- "不存在的商品" - 测试空结果

### 特殊字符测试：
- 空格
- 数字
- 英文字母
- 特殊符号

## 问题记录模板

| 测试项目 | 测试结果 | 问题描述 | 严重程度 | 备注 |
|---------|---------|---------|---------|------|
| 搜索输入 | ✓/✗ | | 高/中/低 | |
| 延迟搜索 | ✓/✗ | | 高/中/低 | |
| 清除功能 | ✓/✗ | | 高/中/低 | |

## 验收标准
- [ ] 搜索输入框正常工作
- [ ] 延迟搜索机制正确
- [ ] 即时搜索功能正常
- [ ] 清除搜索功能正常
- [ ] 搜索结果正确显示
- [ ] 与排序功能兼容
- [ ] 与分页功能兼容
- [ ] 无性能问题
- [ ] 用户体验良好