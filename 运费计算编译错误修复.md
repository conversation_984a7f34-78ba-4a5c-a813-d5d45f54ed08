# 运费计算编译错误修复

## 问题描述
在实现特定地区运费加收功能时，CartService中出现编译错误：
```
E:\project\code\myself\wjsy_shop\server\src\main\java\com\logic\code\service\CartService.java:279:76
java: 找不到符号
符号:   变量 goodsTotalPrice
位置: 类 com.logic.code.service.CartService
```

## 问题原因
在CartService的两个方法中，运费计算代码试图使用`goodsTotalPrice`变量，但该变量在使用时尚未定义。代码执行顺序有误：

**错误的顺序**：
1. 选择收货地址
2. **计算运费（使用未定义的goodsTotalPrice）** ❌
3. 获取购物车数据
4. 计算商品总价（定义goodsTotalPrice）

## 修复方案
调整代码执行顺序，确保在计算运费之前先计算出商品总价：

**正确的顺序**：
1. 选择收货地址
2. 获取购物车数据
3. 计算商品总价（定义goodsTotalPrice）
4. **计算运费（使用已定义的goodsTotalPrice）** ✅

## 修复的方法

### 1. checkoutCartWithPoints方法
**修复前**：
```java
// 选择收货地址
Address checkedAddress = selectAddress(addressId, userInfo.getId());

// 根据收货地址计算运费 - 错误：goodsTotalPrice未定义
BigDecimal freightPrice = freightService.calculateFreight(checkedAddress, goodsTotalPrice);

CartResultVO cartData = this.getCart();
// 计算商品总价
BigDecimal goodsTotalPrice = cartData.getCartTotal().getCheckedGoodsAmount();
```

**修复后**：
```java
// 选择收货地址
Address checkedAddress = selectAddress(addressId, userInfo.getId());

CartResultVO cartData = this.getCart();
// 计算商品总价
BigDecimal goodsTotalPrice = cartData.getCartTotal().getCheckedGoodsAmount();

// 根据收货地址计算运费 - 正确：goodsTotalPrice已定义
BigDecimal freightPrice = freightService.calculateFreight(checkedAddress, goodsTotalPrice);
```

### 2. advancedCheckout方法
应用了相同的修复逻辑，调整了代码执行顺序。

## 验证结果
- ✅ 编译成功，无错误
- ✅ 运费计算逻辑正确
- ✅ 不影响原有功能

## 影响范围
- 仅影响CartService中的两个方法
- 不影响OrderService（该服务中的代码顺序本来就是正确的）
- 不影响前端代码
- 不影响数据库结构

## 测试建议
1. 验证购物车结算流程正常
2. 验证运费计算准确
3. 验证特殊地区和普通地区的运费差异
4. 验证运费与优惠券、积分、余额的组合使用

## 总结
这是一个简单的变量作用域问题，通过调整代码执行顺序得到了完美解决。修复后的代码逻辑更加清晰，运费计算功能可以正常工作。